# Sevalla Database Troubleshooting Guide

## 🔍 Quick Diagnosis

If users are reporting database issues on Sevalla, run these diagnostic tools:

```bash
# Test basic database connectivity
node test-db.js

# Test database initialization process
node test-db-init.js

# Run comprehensive Sevalla diagnostic
node sevalla-diagnostic.js
```

## 🚨 Common Issues and Solutions

### 1. Database Connection Timeout

**Symptoms:**
- "Connection timeout" errors
- App fails to start on Sevalla
- Database initialization hangs

**Solutions:**
```bash
# Check if DATABASE_URL is correctly set in Sevalla environment
echo $DATABASE_URL

# Verify Neon database region matches Sevalla region
# Neon US East (Virginia) works best with Sevalla
```

**Fix in Sevalla Dashboard:**
1. Go to your Sevalla project settings
2. Check Environment Variables
3. Ensure `DATABASE_URL` is set correctly
4. Format: `postgresql://username:password@hostname:port/database?sslmode=require`

### 2. SSL Connection Issues

**Symptoms:**
- "SSL connection required" errors
- "certificate verify failed" errors

**Solutions:**
```bash
# Ensure your DATABASE_URL includes SSL parameters
DATABASE_URL="********************************/db?sslmode=require&channel_binding=require"
```

### 3. Authentication Failures

**Symptoms:**
- "authentication failed" errors
- "password authentication failed" errors

**Solutions:**
1. Verify Neon database credentials
2. Check if password contains special characters (URL encode them)
3. Ensure database user has proper permissions

### 4. Missing Environment Variables

**Symptoms:**
- "DATABASE_URL is not set" errors
- App starts but database features don't work

**Solutions:**
1. Set all required environment variables in Sevalla:
   ```
   DATABASE_URL=your_neon_database_url
   DISCORD_TOKEN=your_discord_bot_token
   BOT_CLIENT_ID=your_bot_client_id
   BOT_CLIENT_SECRET=your_bot_client_secret
   WEB_URL=https://your-frontend.sevalla.app
   NEXT_PUBLIC_API_ENDPOINT=https://your-backend.sevalla.app
   NODE_ENV=production
   PORT=8080
   ```

### 5. Table Creation Failures

**Symptoms:**
- "permission denied" errors during table creation
- "relation does not exist" errors

**Solutions:**
1. Verify database user has CREATE TABLE permissions
2. Check if Neon database has sufficient storage
3. Ensure PostgreSQL version compatibility (requires PostgreSQL 12+)

## 🔧 Step-by-Step Troubleshooting

### Step 1: Verify Local Configuration

```bash
# Test locally first
npm start

# Check health endpoint
curl http://localhost:8080/health
```

### Step 2: Test Database Connection

```bash
# Run comprehensive database test
node test-db.js

# Expected output should show:
# ✅ Database connection successful
# ✅ All tables exist
# ✅ Write operations successful
```

### Step 3: Check Sevalla Environment

1. **Log into Sevalla Dashboard**
2. **Go to your backend project**
3. **Check Environment Variables tab**
4. **Verify all required variables are set**

### Step 4: Check Sevalla Deployment Logs

1. **Go to Sevalla Dashboard**
2. **Select your backend deployment**
3. **Check "Logs" tab for errors**
4. **Look for database-related error messages**

### Step 5: Test Neon Database Directly

```bash
# Test connection to Neon from your local machine
node -e "
const { Pool } = require('pg');
const pool = new Pool({
  connectionString: 'YOUR_DATABASE_URL',
  ssl: { rejectUnauthorized: false }
});
pool.query('SELECT NOW()', (err, res) => {
  if (err) console.error('Error:', err.message);
  else console.log('Success:', res.rows[0]);
  pool.end();
});
"
```

## 🛠️ Advanced Troubleshooting

### Check Neon Database Status

1. **Log into Neon Console**
2. **Check database status**
3. **Verify connection limits**
4. **Check if database is suspended**

### Network Connectivity Issues

```bash
# Test if Sevalla can reach Neon
# (Run this in Sevalla terminal if available)
nslookup your-neon-hostname.neon.tech
```

### Database Connection Pool Issues

If you see connection pool errors:

1. **Reduce max connections in database.ts:**
   ```typescript
   pool = new Pool({
     connectionString: process.env.DATABASE_URL,
     ssl: { rejectUnauthorized: false },
     max: 5, // Reduce from default 10
     idleTimeoutMillis: 30000,
     connectionTimeoutMillis: 10000
   });
   ```

2. **Check Neon connection limits**
3. **Ensure proper connection cleanup**

## 📋 Checklist for Users

### Before Deploying to Sevalla:

- [ ] Database works locally (`node test-db.js` passes)
- [ ] All environment variables are set
- [ ] DATABASE_URL includes SSL parameters
- [ ] Neon database is in US East region (recommended)
- [ ] Discord bot token is valid
- [ ] Frontend and backend URLs are correct

### After Deploying to Sevalla:

- [ ] Check Sevalla deployment logs
- [ ] Test health endpoint: `https://your-backend.sevalla.app/health`
- [ ] Verify environment variables in Sevalla dashboard
- [ ] Test database connection from Sevalla
- [ ] Check Neon database metrics

## 🆘 When All Else Fails

### 1. Create a New Neon Database

Sometimes the issue is with the specific Neon database:

1. Create a new Neon project
2. Copy the new DATABASE_URL
3. Update Sevalla environment variables
4. Redeploy

### 2. Check Sevalla Status

- Visit Sevalla status page
- Check for ongoing issues
- Contact Sevalla support if needed

### 3. Alternative Database Testing

Create a minimal test to isolate the issue:

```javascript
// minimal-db-test.js
require('dotenv').config();
const { Pool } = require('pg');

async function minimalTest() {
  console.log('Testing minimal database connection...');
  
  if (!process.env.DATABASE_URL) {
    console.error('DATABASE_URL not set');
    return;
  }
  
  const pool = new Pool({
    connectionString: process.env.DATABASE_URL,
    ssl: { rejectUnauthorized: false }
  });
  
  try {
    const result = await pool.query('SELECT 1 as test');
    console.log('✅ Success:', result.rows[0]);
  } catch (err) {
    console.error('❌ Error:', err.message);
  } finally {
    await pool.end();
  }
}

minimalTest();
```

## 📞 Getting Help

If users continue to have issues:

1. **Run all diagnostic tools and share results**
2. **Provide Sevalla deployment logs**
3. **Share Neon database connection details (without credentials)**
4. **Test with a fresh Neon database**
5. **Contact Sevalla support with specific error messages**

## 🔄 Regular Maintenance

To prevent future issues:

1. **Monitor Neon database metrics**
2. **Keep connection pool settings optimized**
3. **Regularly test database connectivity**
4. **Update dependencies periodically**
5. **Monitor Sevalla deployment health**
