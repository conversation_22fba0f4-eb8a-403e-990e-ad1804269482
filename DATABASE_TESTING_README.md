# Database Testing Tools for Sevalla Deployment

This repository includes comprehensive database testing tools to help diagnose and resolve database issues when deploying your Discord bot to Sevalla.

## 🛠️ Available Testing Tools

### 1. `user-db-test.js` - Primary User Diagnostic Tool
**Use this first** - Comprehensive test for users experiencing database issues.

```bash
node user-db-test.js
```

**What it tests:**
- Environment variables configuration
- Database connection and timing
- Database permissions (CREATE, INSERT, SELECT)
- Discord bot specific table structures
- Sevalla-specific configuration
- JSONB support for advanced features

**Output:** Clear pass/fail results with specific solutions for each issue.

### 2. `test-db.js` - Enhanced Database Connection Test
Comprehensive database connectivity and functionality test.

```bash
node test-db.js
```

**What it tests:**
- Basic connection with timing
- Database version and capabilities
- All required Discord bot tables
- Read/write operations
- Connection pool status

### 3. `test-db-init.js` - Database Initialization Test
Tests the exact database initialization process used by the application.

```bash
node test-db-init.js
```

**What it tests:**
- Table creation sequence
- Sample data operations
- Cleanup procedures
- Initialization error handling

### 4. `sevalla-diagnostic.js` - Complete Sevalla Deployment Check
Full diagnostic for Sevalla deployment readiness.

```bash
node sevalla-diagnostic.js
```

**What it tests:**
- All environment variables
- URL configurations
- Discord token validation
- CORS setup
- Common deployment issues

## 🚀 Quick Start for Users

If you're having database issues on Sevalla, follow these steps:

### Step 1: Run the User Diagnostic
```bash
node user-db-test.js
```

This will identify the most common issues and provide specific solutions.

### Step 2: Fix Any Issues Found
Follow the solutions provided by the diagnostic tool.

### Step 3: Run Full Diagnostic
```bash
node sevalla-diagnostic.js
```

This ensures your complete Sevalla configuration is correct.

### Step 4: Test Database Initialization
```bash
node test-db-init.js
```

This verifies that all Discord bot tables can be created properly.

## 🔧 Common Issues and Quick Fixes

### Issue: "DATABASE_URL is not set"
**Solution:**
```bash
# Add to your .env file:
DATABASE_URL="postgresql://username:password@hostname:port/database?sslmode=require"
```

### Issue: "Connection timeout"
**Solutions:**
1. Ensure your Neon database is in US East region (matches Sevalla)
2. Check if your Neon database is active (not suspended)
3. Verify your DATABASE_URL is correct

### Issue: "SSL connection required"
**Solution:**
```bash
# Ensure your DATABASE_URL includes SSL parameters:
DATABASE_URL="postgresql://user:pass@host:port/db?sslmode=require&channel_binding=require"
```

### Issue: "Permission denied"
**Solutions:**
1. Verify your database user has CREATE TABLE permissions
2. Check if you're using the correct database user (usually ends with `_owner`)
3. Ensure your Neon database has sufficient storage

## 📋 Environment Variables Checklist

Ensure these are set in your Sevalla environment:

### Required Variables:
```bash
DATABASE_URL="postgresql://..."
DISCORD_TOKEN="your_discord_bot_token"
BOT_CLIENT_ID="your_bot_client_id"
BOT_CLIENT_SECRET="your_bot_client_secret"
WEB_URL="https://your-frontend.sevalla.app"
NEXT_PUBLIC_API_ENDPOINT="https://your-backend.sevalla.app"
NODE_ENV="production"
PORT="8080"
```

### Optional Variables:
```bash
INTERNAL_API_ENDPOINT="https://your-backend.sevalla.app"
WHOP_API_KEY="your_whop_api_key"
NEXT_PUBLIC_WHOP_APP_ID="your_whop_app_id"
```

## 🔍 Interpreting Test Results

### ✅ Green (Passed)
Everything is working correctly. No action needed.

### ⚠️ Yellow (Warning)
Configuration might not be optimal but won't prevent deployment. Review and fix if possible.

### ❌ Red (Failed)
Critical issue that will prevent successful deployment. Must be fixed before deploying to Sevalla.

## 🆘 When Tests Still Fail

If all tests pass locally but you still have issues on Sevalla:

1. **Check Sevalla Deployment Logs:**
   - Go to Sevalla Dashboard
   - Select your backend project
   - Check the "Logs" tab for errors

2. **Verify Environment Variables in Sevalla:**
   - Ensure all variables are set in Sevalla dashboard
   - Check for typos or missing values
   - Verify URLs point to correct Sevalla deployments

3. **Test Neon Database Directly:**
   ```bash
   # Test from your local machine
   psql "postgresql://username:password@hostname:port/database?sslmode=require"
   ```

4. **Create a New Neon Database:**
   - Sometimes the issue is with the specific database
   - Create a fresh Neon project
   - Update your DATABASE_URL

## 📞 Getting Additional Help

If you continue to have issues after running all tests:

1. **Share Test Results:** Run `node user-db-test.js` and share the complete output
2. **Provide Sevalla Logs:** Include relevant error messages from Sevalla deployment logs
3. **Check Neon Status:** Verify your Neon database is active and accessible
4. **Test with Minimal Configuration:** Try with a fresh .env file with only essential variables

## 🔄 Regular Maintenance

To prevent future database issues:

1. **Run tests periodically:** `node user-db-test.js`
2. **Monitor Neon database metrics**
3. **Keep dependencies updated**
4. **Check Sevalla deployment health regularly**
5. **Backup your database configuration**

## 📚 Additional Resources

- [Sevalla Documentation](https://docs.sevalla.com)
- [Neon Database Documentation](https://neon.tech/docs)
- [Discord Developer Portal](https://discord.com/developers/applications)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)

---

**Need immediate help?** Run `node user-db-test.js` and follow the specific solutions provided for your issues.
