# BYOK (Bring Your Own Key) Implementation Guide

## Overview

The BYOK (Bring Your Own Key) feature allows administrators to use their own API keys for AI services instead of relying on system-provided keys. This provides better cost control, usage tracking, and compliance with organizational policies.

## Features

### Supported AI Providers

- **OpenAI**: GPT-4, GPT-3.5 Turbo, and other OpenAI models
- **Anthropic**: Claude 3 (Opus, Sonnet, Haiku) models
- **Google AI**: Gemini Pro and other Google AI models

### Key Management

- **Secure Storage**: API keys are encrypted using AES-256-GCM encryption
- **Validation**: Keys are validated against provider APIs before activation
- **Multiple Keys**: Support for multiple keys per provider for redundancy
- **Active/Inactive States**: Keys can be enabled or disabled without deletion
- **Fallback Support**: Option to fallback to system keys if <PERSON><PERSON><PERSON> fails

## Implementation Details

### Backend Components

#### 1. Encryption Service (`src/lib/encryption.ts`)
- Handles secure encryption/decryption of API keys
- Uses AES-256-GCM with random initialization vectors
- Includes authentication tags for integrity verification

#### 2. BYOK API Endpoints
- `POST /api/admin/byok` - Create new BYOK key
- `GET /api/admin/byok` - List all BYOK keys
- `GET /api/admin/byok/[keyId]` - Get specific key details
- `PUT /api/admin/byok/[keyId]` - Update existing key
- `DELETE /api/admin/byok/[keyId]` - Delete key
- `POST /api/admin/byok/validate` - Validate key with provider

#### 3. Type Definitions (`src/types/byok.ts`)
- Comprehensive TypeScript interfaces for BYOK functionality
- Provider configurations and validation results
- Error handling types

### Frontend Components

#### 1. BYOK Management Panel (`src/components/admin/BYOKManagementPanel.tsx`)
- Complete interface for managing BYOK keys
- Key creation, editing, validation, and deletion
- Real-time validation status display
- Provider-specific configuration options

#### 2. AI Agent Integration
- BYOK configuration options in AI agent settings
- Provider-specific key selection
- Fallback configuration options

## Configuration Guide

### Environment Variables

```env
# Required: Master encryption key (32+ characters)
BYOK_MASTER_KEY=your-secure-master-key-32-characters-minimum

# Optional: Admin user IDs (comma-separated)
ADMIN_USER_IDS=123456789012345678,987654321098765432
```

### Setting Up BYOK Keys

1. **Access Admin Settings**
   - Navigate to `/admin/settings`
   - Click on the "BYOK" tab

2. **Add New Key**
   - Click "Add Key" button
   - Select AI provider (OpenAI, Anthropic, Google)
   - Enter a descriptive name for the key
   - Paste your API key
   - Enable/disable the key as needed

3. **Validate Key**
   - Click the validation button next to any key
   - System will test the key with the provider
   - Validation status will be updated

4. **Configure AI Agents**
   - Go to AI Agents configuration
   - Enable "Use Bring Your Own Key (BYOK)"
   - Select a validated key for the chosen provider
   - Configure fallback options

## Security Considerations

### Encryption
- All API keys are encrypted before storage
- Encryption uses industry-standard AES-256-GCM
- Each key has a unique initialization vector
- Authentication tags prevent tampering

### Access Control
- Only admin users can manage BYOK keys
- API endpoints require admin authentication
- Encrypted keys are never returned in API responses

### Key Rotation
- Keys can be updated without losing configuration
- Old keys are securely overwritten
- Validation status is reset when keys are updated

## API Reference

### Create BYOK Key

```typescript
POST /api/admin/byok
Content-Type: application/json

{
  "provider": "openai",
  "keyName": "Production OpenAI Key",
  "apiKey": "sk-...",
  "isActive": true
}
```

### Validate BYOK Key

```typescript
POST /api/admin/byok/validate
Content-Type: application/json

{
  "keyId": "byok_abc123",
  "testModel": "gpt-3.5-turbo"
}
```

### Response Format

```typescript
{
  "id": "byok_abc123",
  "provider": "openai",
  "keyName": "Production OpenAI Key",
  "isActive": true,
  "isValid": true,
  "lastValidated": "2024-01-15T10:30:00Z",
  "createdAt": "2024-01-15T09:00:00Z",
  "updatedAt": "2024-01-15T10:30:00Z",
  "createdBy": "123456789012345678"
}
```

## Error Handling

### Common Error Codes
- `INVALID_KEY`: API key format is incorrect
- `ENCRYPTION_FAILED`: Failed to encrypt the key
- `VALIDATION_FAILED`: Key validation with provider failed
- `PROVIDER_ERROR`: Provider-specific error occurred
- `QUOTA_EXCEEDED`: API quota limits reached
- `RATE_LIMITED`: Rate limit exceeded

### Error Response Format

```typescript
{
  "error": "VALIDATION_FAILED",
  "message": "API key is invalid or expired",
  "provider": "openai",
  "keyId": "byok_abc123",
  "details": {
    "statusCode": 401,
    "providerMessage": "Invalid API key"
  }
}
```

## Best Practices

### Key Management
1. Use descriptive names for keys (e.g., "Production OpenAI", "Development Claude")
2. Regularly validate keys to ensure they remain active
3. Monitor usage through provider dashboards
4. Rotate keys periodically for security

### Configuration
1. Always enable fallback to system keys for reliability
2. Test BYOK configuration in development before production
3. Monitor AI agent performance after enabling BYOK
4. Keep backup keys for critical applications

### Security
1. Use strong, unique master encryption keys
2. Limit admin access to trusted users only
3. Regularly audit BYOK key usage
4. Monitor for unusual API usage patterns

## Troubleshooting

### Key Validation Fails
1. Check API key format matches provider requirements
2. Verify key has necessary permissions
3. Ensure key hasn't expired or been revoked
4. Check provider service status

### AI Agents Not Using BYOK
1. Verify BYOK is enabled in agent configuration
2. Check selected key is active and valid
3. Ensure key provider matches agent model provider
4. Review fallback settings

### Encryption Errors
1. Verify BYOK_MASTER_KEY environment variable is set
2. Ensure master key is at least 32 characters
3. Check server has sufficient entropy for encryption
4. Review server logs for detailed error messages

## Migration Guide

### From System Keys to BYOK
1. Add and validate BYOK keys for your providers
2. Configure AI agents to use BYOK with fallback enabled
3. Monitor performance and usage
4. Gradually disable fallback once confident in BYOK stability

### Updating Existing Keys
1. Add new key with different name
2. Validate new key
3. Update AI agent configurations to use new key
4. Delete old key after confirming new key works

## Support

For technical support or questions about BYOK implementation:
1. Check server logs for detailed error messages
2. Verify environment configuration
3. Test with provider APIs directly
4. Review this documentation for troubleshooting steps

## Changelog

### Version 1.0.0
- Initial BYOK implementation
- Support for OpenAI, Anthropic, and Google AI
- Secure key encryption and storage
- Admin interface for key management
- AI agent integration
- Comprehensive validation system
