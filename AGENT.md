# AGENT.md - Discord Bot Dashboard Backend

## Build/Lint/Test Commands
- **Build**: `pnpm run build` (Next.js build)
- **Development**: `pnpm run dev` (starts on PORT=8080)
- **Production**: `pnpm run start:prod`
- **Test Discord API**: `pnpm run test:discord` (runs Discord integration tests)
- **Test Whop**: `pnpm run test:whop` (tests Whop integration)
- **Health Check**: `curl http://localhost:8080/api/health`
- **Single Test**: `npx jest <testfile>` (Jest with Next.js config)
- **Se<PERSON><PERSON> Deploy**: `pnpm run sevalla:build && pnpm run sevalla:start`

## Architecture & Core Stack
- **Next.js 15.4.2** - App Router with TypeScript strict mode
- **Database**: PostgreSQL with connection pooling (`lib/database.ts`)
- **Authentication**: Custom Discord OAuth2 (NOT NextAuth despite dependency)
- **External APIs**: Discord.js 14.14.1, Whop API @0.0.36
- **Runtime**: Node.js >=18.17.0, pnpm package manager
- **Deployment**: Sevalla hosting platform

## Authentication Architecture (IMPORTANT)
**⚠️ Uses custom Discord OAuth2, NOT NextAuth**
- **Login Flow**: `/api/auth/login` → Discord OAuth → `/api/auth/callback`
- **CSRF Protection**: State tokens with `locale:token` format
- **Session Management**: Bearer tokens in Authorization headers
- **Security**: HTTPOnly cookies, SameSite=lax, secure in production
- **Middleware**: Protects `/api/guilds/*` and `/api/whop/*` routes

## Database Architecture
- **Connection**: PostgreSQL pool with SSL in production
- **Pattern**: Generic service factory for feature-based CRUD
- **Services**: 11 feature services (welcome-message, music, gaming, etc.)
- **Schema**: Guild-based features with JSON configuration columns
- **Migrations**: SQL files in `/migrations/` directory

## Bot Service & Discord Integration
- **BotService**: Singleton Discord.js client with feature management
- **Intents**: Guilds, GuildMembers, GuildPresences
- **Features**: 11 modular features per guild (welcome, music, moderation, etc.)
- **Permissions**: Built-in guild permission checking
- **Rate Limiting**: Discord API rate limiting in `discord-utils.ts`

## API Route Structure
```
/api/
├── auth/          # Discord OAuth2 flow
├── health/        # Health check endpoint
├── guilds/[guild]/# Guild-specific features (protected)
├── whop/          # Whop integration (protected)
└── ping/          # Basic connectivity test
```

## CORS & Security
- **Production Origins**: `discordbot-energex-jkhvk.sevalla.app`
- **Development**: localhost:3000, 3001, 8080
- **Headers**: Credentials allowed, specific origins only
- **Methods**: GET, POST, PUT, DELETE, PATCH supported

## Code Style & Conventions
- **TypeScript strict mode** with enhanced error checking
- **ESLint**: Next.js + Prettier config, @typescript-eslint
- **Imports**: Use `@/` for root, `@/lib/*`, `@/src/*` path aliases
- **API Routes**: Export named functions (GET, POST, etc.) in route.ts files
- **Error Handling**: Use try/catch with detailed console.error logging
- **Database**: Generic service factory pattern with upsert/findUnique methods
- **Naming**: kebab-case for API routes, camelCase for variables, PascalCase for components
