import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { Client, Message, Interaction } from 'discord.js';
import { Context, On, SlashCommand, SlashCommandContext } from 'necord';
import { AgentsService } from '../agents.service';
import { SchedulerService } from './scheduler.service';
import { ChannelRoutingService } from './channel-routing.service';
import { PersonalGrowthCoach } from '../types/personal-growth-coach';
import { IntakeSpecialist } from '../types/intake-specialist';
import { ProgressTracker } from '../types/progress-tracker';

@Injectable()
export class AIAgentIntegrationService implements OnModuleInit {
  private readonly logger = new Logger(AIAgentIntegrationService.name);

  constructor(
    private readonly client: Client,
    private readonly agentsService: AgentsService,
    private readonly schedulerService: SchedulerService,
    private readonly channelRoutingService: ChannelRoutingService,
    private readonly personalGrowthCoach: PersonalGrowthCoach,
    private readonly intakeSpecialist: IntakeSpecialist,
    private readonly progressTracker: ProgressTracker,
  ) {}

  async onModuleInit() {
    this.logger.log('🤖 Initializing AI Agent Integration Service...');
    
    try {
      // Initialize scheduler for proactive messaging
      await this.schedulerService.initialize();
      
      // Initialize channel routing
      await this.channelRoutingService.initialize();
      
      this.logger.log('✅ AI Agent Integration Service initialized successfully');
      this.logger.log('🎯 Available AI agents: Personal Growth Coach, Intake Specialist, Progress Tracker');
    } catch (error) {
      this.logger.error('❌ Failed to initialize AI Agent Integration Service:', error);
      throw error;
    }
  }

  @On('messageCreate')
  async handleMessage(@Context() [message]: [Message]) {
    if (message.author.bot) return;

    try {
      // Check if this is an AI agent channel or DM
      const shouldProcess = await this.shouldProcessMessage(message);
      if (!shouldProcess) return;

      // Route message to appropriate agent
      const agentType = await this.channelRoutingService.routeMessage(message);
      if (!agentType) return;

      // Process with the determined agent
      await this.processWithAgent(message, agentType);

      // Log the interaction
      await this.agentsService.logInteraction({
        userId: message.author.id,
        agentType: agentType as 'personal_growth_coach' | 'intake_specialist' | 'progress_tracker' | 'general',
        interactionType: 'message',
        content: message.content,
        guildId: message.guild?.id,
        channelId: message.channel.id,
      });
    } catch (error) {
      this.logger.error('Failed to handle message for AI agents:', error);
    }
  }

  @SlashCommand({
    name: 'coach',
    description: 'Start a conversation with your personal growth coach',
  })
  async onCoachCommand(@Context() [interaction]: SlashCommandContext) {
    try {
      const response = await this.personalGrowthCoach.handleInteraction(
        interaction.user.id,
        'greeting',
        'Hello, I\'d like to start working with a personal growth coach.',
        { guildId: interaction.guild?.id, channelId: interaction.channel?.id }
      );

      await interaction.reply({ 
        content: response,
        ephemeral: true,
      });

      await this.agentsService.logInteraction({
        userId: interaction.user.id,
        agentType: 'personal_growth_coach',
        interactionType: 'command',
        content: '/coach command',
        response,
        guildId: interaction.guild?.id,
        channelId: interaction.channel?.id,
      });
    } catch (error) {
      this.logger.error('Coach command failed:', error);
      await interaction.reply({
        content: '❌ Sorry, I\'m having trouble connecting to your coach right now.',
        ephemeral: true,
      });
    }
  }

  @SlashCommand({
    name: 'intake',
    description: 'Complete your intake assessment with our specialist',
  })
  async onIntakeCommand(@Context() [interaction]: SlashCommandContext) {
    try {
      const response = await this.intakeSpecialist.handleInteraction(
        interaction.user.id,
        'assessment',
        'I\'d like to complete my intake assessment.',
        { guildId: interaction.guild?.id, channelId: interaction.channel?.id }
      );

      await interaction.reply({ 
        content: response,
        ephemeral: true,
      });

      await this.agentsService.logInteraction({
        userId: interaction.user.id,
        agentType: 'intake_specialist',
        interactionType: 'command',
        content: '/intake command',
        response,
        guildId: interaction.guild?.id,
        channelId: interaction.channel?.id,
      });
    } catch (error) {
      this.logger.error('Intake command failed:', error);
      await interaction.reply({
        content: '❌ Sorry, the intake specialist is unavailable right now.',
        ephemeral: true,
      });
    }
  }

  @SlashCommand({
    name: 'progress',
    description: 'Check your progress and goals with the progress tracker',
  })
  async onProgressCommand(@Context() [interaction]: SlashCommandContext) {
    try {
      const response = await this.progressTracker.handleInteraction(
        interaction.user.id,
        'check_progress',
        'I\'d like to review my progress and goals.',
        { guildId: interaction.guild?.id, channelId: interaction.channel?.id }
      );

      await interaction.reply({ 
        content: response,
        ephemeral: true,
      });

      await this.agentsService.logInteraction({
        userId: interaction.user.id,
        agentType: 'progress_tracker',
        interactionType: 'command',
        content: '/progress command',
        response,
        guildId: interaction.guild?.id,
        channelId: interaction.channel?.id,
      });
    } catch (error) {
      this.logger.error('Progress command failed:', error);
      await interaction.reply({
        content: '❌ Sorry, the progress tracker is not available right now.',
        ephemeral: true,
      });
    }
  }

  private async shouldProcessMessage(message: Message): Promise<boolean> {
    // Check if it's a DM
    if (!message.guild) return true;

    // Check if it's in an AI agent configured channel
    const config = await this.agentsService.getAgentConfig(message.guild.id, 'general');
    if (!config) return false;

    // Check if the channel is in the configured channels list
    const configuredChannels = config.configuration?.channels || [];
    return configuredChannels.includes(message.channel.id) || configuredChannels.length === 0;
  }

  private async processWithAgent(message: Message, agentType: string): Promise<void> {
    try {
      let response: string;

      switch (agentType) {
        case 'personal_growth_coach':
          response = await this.personalGrowthCoach.handleInteraction(
            message.author.id,
            'message',
            message.content,
            { guildId: message.guild?.id, channelId: message.channel.id }
          );
          break;
        case 'intake_specialist':
          response = await this.intakeSpecialist.handleInteraction(
            message.author.id,
            'message',
            message.content,
            { guildId: message.guild?.id, channelId: message.channel.id }
          );
          break;
        case 'progress_tracker':
          response = await this.progressTracker.handleInteraction(
            message.author.id,
            'message',
            message.content,
            { guildId: message.guild?.id, channelId: message.channel.id }
          );
          break;
        default:
          this.logger.warn(`Unknown agent type: ${agentType}`);
          return;
      }

      if (response && response.trim()) {
        await message.reply(response);
      }
    } catch (error) {
      this.logger.error(`Failed to process message with agent ${agentType}:`, error);
    }
  }

  async getAgentStats(): Promise<any> {
    try {
      // In a real implementation, this would gather actual statistics
      return {
        totalInteractions: await this.getTotalInteractions(),
        activeAgents: ['personal_growth_coach', 'intake_specialist', 'progress_tracker'],
        scheduledTasks: await this.schedulerService.getScheduledTaskCount(),
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('Failed to get agent stats:', error);
      return {
        error: 'Failed to retrieve agent statistics',
        timestamp: new Date().toISOString(),
      };
    }
  }

  private async getTotalInteractions(): Promise<number> {
    // This would query the database for interaction counts
    // For now, return a placeholder
    return 0;
  }
}