import { Inject, Injectable, Logger } from '@nestjs/common';
import { and, eq } from 'drizzle-orm';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { DATABASE_CONNECTION } from '../core/database/database.service';
import {
    AIAgentConfig,
    AgentMemory,
    AgentType,
    InteractionType,
    MemoryType,
    agentInteractions,
    agentMemory,
    aiAgentConfigs
} from '../core/database/schema';

@Injectable()
export class AgentsService {
  private readonly logger = new Logger(AgentsService.name);

  constructor(
    @Inject(DATABASE_CONNECTION) private db: NodePgDatabase,
  ) {}

  async getAgentConfig(guildId: string, agentType: string): Promise<AIAgentConfig | null> {
    try {
      const result = await this.db.select().from(aiAgentConfigs)
        .where(and(
          eq(aiAgentConfigs.guildId, guildId),
          eq(aiAgentConfigs.agentType, agentType),
          eq(aiAgentConfigs.enabled, true)
        ))
        .limit(1);
      return result[0] || null;
    } catch (error) {
      this.logger.error(`Failed to get agent config for ${guildId}:${agentType}`, error);
      return null;
    }
  }

  async logInteraction(data: {
    userId: string;
    agentType: AgentType;
    interactionType: InteractionType;
    content: string;
    response?: string;
    guildId?: string;
    channelId?: string;
  }): Promise<void> {
    try {
      const interaction = {
        ...data,
        status: 'completed' as const,
      };
      await this.db.insert(agentInteractions).values(interaction);
    } catch (error) {
      this.logger.error('Failed to log agent interaction:', error);
    }
  }

  async storeMemory(data: {
    userId: string;
    memoryType: MemoryType;
    key: string;
    value: Record<string, any>;
    context?: string;
    importance?: number;
  }): Promise<void> {
    try {
      const memory = {
        ...data,
        importance: data.importance || 1,
        lastAccessedAt: new Date(),
        accessCount: 0,
      };
      await this.db.insert(agentMemory).values(memory);
    } catch (error) {
      this.logger.error('Failed to store agent memory:', error);
    }
  }

  async getMemory(userId: string, key: string): Promise<AgentMemory | null> {
    try {
      const result = await this.db.select().from(agentMemory)
        .where(and(
          eq(agentMemory.userId, userId),
          eq(agentMemory.key, key)
        ))
        .limit(1);
      
      const memory = result[0] || null;
      
      if (memory) {
        // Update access count
        await this.db.update(agentMemory)
          .set({
            accessCount: (memory.accessCount || 0) + 1,
            lastAccessedAt: new Date()
          } as any)
          .where(eq(agentMemory.id, memory.id));
      }
      
      return memory;
    } catch (error) {
      this.logger.error('Failed to get agent memory:', error);
      return null;
    }
  }
}