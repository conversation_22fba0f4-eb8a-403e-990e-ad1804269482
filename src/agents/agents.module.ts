import { Modu<PERSON> } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { AgentsService } from './agents.service';
import { AIAgentIntegrationService } from './integration/ai-agent-integration.service';
import { SchedulerService } from './integration/scheduler.service';
import { ChannelRoutingService } from './integration/channel-routing.service';
import { PersonalGrowthCoach } from './types/personal-growth-coach';
import { IntakeSpecialist } from './types/intake-specialist';
import { ProgressTracker } from './types/progress-tracker';
import { DatabaseModule } from '../core/database/database.module';
import { DiscordModule } from '../discord/discord.module';

@Module({
  imports: [DatabaseModule, DiscordModule, ScheduleModule.forRoot()],
  providers: [
    AgentsService,
    AIAgentIntegrationService,
    SchedulerService,
    ChannelRoutingService,
    PersonalGrowthCoach,
    IntakeSpecialist,
    ProgressTracker,
  ],
  exports: [
    AgentsService,
    AIAgentIntegrationService,
    SchedulerService,
    ChannelRoutingService,
  ],
})
export class AgentsModule {}