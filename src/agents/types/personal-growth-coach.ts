import { Injectable, Logger } from '@nestjs/common';
import { AgentsService } from '../agents.service';

@Injectable()
export class PersonalGrowthCoach {
  private readonly logger = new Logger(PersonalGrowthCoach.name);
  
  // Coach personality and response templates
  private readonly coachPersonality = {
    tone: 'supportive',
    style: 'encouraging',
    expertise: ['personal development', 'goal setting', 'motivation', 'habit formation'],
  };

  private readonly responseTemplates = {
    greeting: [
      'Hello! I\'m excited to be your personal growth coach. What area of your life would you like to focus on improving today?',
      'Welcome! As your personal growth coach, I\'m here to help you unlock your potential. What\'s on your mind?',
      'Hi there! I\'m here to support your journey of personal growth. What would you like to work on together?',
    ],
    motivation: [
      'Remember, every small step forward is progress. You\'re capable of amazing things! 🌟',
      'Your commitment to growth is inspiring. Keep pushing forward - you\'ve got this! 💪',
      'Progress isn\'t always linear, but you\'re moving in the right direction. Stay focused on your goals! 🎯',
      'Each day is a new opportunity to become the person you want to be. What will you choose today? 🚀',
    ],
    goal_setting: [
      'Let\'s break that goal down into smaller, actionable steps. What\'s the first thing you can do today?',
      'Great goal! Now, let\'s make it SMART: Specific, Measurable, Achievable, Relevant, and Time-bound.',
      'I love your ambition! Let\'s create a roadmap to get you there. What does success look like to you?',
    ],
    check_in: [
      'How are you feeling about your progress today? What\'s one thing you\'re proud of?',
      'It\'s check-in time! What challenges are you facing, and how can we tackle them together?',
      'Let\'s reflect on your recent wins. What\'s working well for you right now?',
    ],
  };

  constructor(private readonly agentsService: AgentsService) {}

  async handleInteraction(
    userId: string,
    interactionType: string,
    content: string,
    context?: any
  ): Promise<string> {
    try {
      // Get user's coaching history and context
      const userContext = await this.getUserContext(userId);
      
      // Generate personalized response based on interaction type
      let response = await this.generateResponse(
        interactionType,
        content,
        userContext,
        context
      );

      // Store the interaction for future context
      await this.storeInteractionContext(userId, interactionType, content, response);

      return response;
    } catch (error) {
      this.logger.error(`Failed to handle interaction for user ${userId}:`, error);
      return 'I apologize, but I\'m having trouble processing your request right now. Please try again in a moment.';
    }
  }

  private async generateResponse(
    interactionType: string,
    content: string,
    userContext: any,
    context?: any
  ): Promise<string> {
    switch (interactionType) {
      case 'greeting':
        return this.getRandomTemplate('greeting');
      
      case 'motivation':
        return this.getMotivationalResponse(userContext);
      
      case 'goal_setting':
        return this.getGoalSettingResponse(content, userContext);
      
      case 'check_in':
        return this.getCheckinResponse(userContext, context?.isScheduled);
      
      case 'message':
        return await this.getContextualResponse(content, userContext);
      
      default:
        return this.getDefaultResponse(content, userContext);
    }
  }

  private getRandomTemplate(type: string): string {
    const templates = this.responseTemplates[type as keyof typeof this.responseTemplates] || [];
    if (templates.length === 0) {
      return 'I\'m here to help you grow and achieve your goals!';
    }
    return templates[Math.floor(Math.random() * templates.length)];
  }

  private getMotivationalResponse(userContext: any): string {
    const baseMotivation = this.getRandomTemplate('motivation');
    
    // Personalize based on user context
    if (userContext.recentChallenges) {
      return `${baseMotivation}\n\nI know you\'ve been working through some challenges lately. Remember, obstacles are opportunities in disguise.`;
    }
    
    if (userContext.recentWins) {
      return `${baseMotivation}\n\nI\'m particularly proud of your recent progress. Keep building on that momentum!`;
    }
    
    return baseMotivation;
  }

  private getGoalSettingResponse(content: string, userContext: any): string {
    const baseResponse = this.getRandomTemplate('goal_setting');
    
    // Analyze the goal mentioned in the content
    const isSpecific = content.length > 50;
    const hasTimeframe = /\b(day|week|month|year|by\s+\w+)\b/i.test(content);
    
    let advice = baseResponse;
    
    if (!isSpecific) {
      advice += '\n\nTry to be more specific about what exactly you want to achieve.';
    }
    
    if (!hasTimeframe) {
      advice += '\n\nConsider adding a timeline - when would you like to achieve this?';
    }
    
    return advice;
  }

  private getCheckinResponse(userContext: any, isScheduled?: boolean): string {
    const baseCheckin = this.getRandomTemplate('check_in');
    
    if (isScheduled) {
      return `Good morning! ☀️ ${baseCheckin}\n\nTake a moment to reflect on yesterday and set your intention for today.`;
    }
    
    return baseCheckin;
  }

  private async getContextualResponse(content: string, userContext: any): Promise<string> {
    const contentLower = content.toLowerCase();
    
    // Detect emotional state
    if (this.isNegativeEmotion(contentLower)) {
      return this.getEmpatheticResponse(content, userContext);
    }
    
    if (this.isPositiveEmotion(contentLower)) {
      return this.getCelebrativeResponse(content, userContext);
    }
    
    // Detect question
    if (content.includes('?')) {
      return this.getInquisitiveResponse(content, userContext);
    }
    
    // Detect goal or aspiration
    if (this.isGoalRelated(contentLower)) {
      return this.getGoalSupportResponse(content, userContext);
    }
    
    return this.getGeneralSupportResponse(content, userContext);
  }

  private isNegativeEmotion(content: string): boolean {
    const negativeWords = ['frustrated', 'stuck', 'overwhelmed', 'difficult', 'hard', 'struggle', 'fail', 'can\'t'];
    return negativeWords.some(word => content.includes(word));
  }

  private isPositiveEmotion(content: string): boolean {
    const positiveWords = ['excited', 'happy', 'achieved', 'success', 'accomplished', 'proud', 'great', 'amazing'];
    return positiveWords.some(word => content.includes(word));
  }

  private isGoalRelated(content: string): boolean {
    const goalWords = ['want to', 'goal', 'achieve', 'improve', 'get better', 'work on', 'focus on'];
    return goalWords.some(phrase => content.includes(phrase));
  }

  private getEmpatheticResponse(content: string, userContext: any): string {
    return `I hear that you\'re facing some challenges right now, and that\'s completely normal. Everyone goes through difficult moments on their growth journey.\n\nWhat\'s one small step you could take today to move forward? Remember, progress doesn\'t have to be perfect - it just has to be consistent.`;
  }

  private getCelebrativeResponse(content: string, userContext: any): string {
    return `That\'s fantastic! 🎉 I\'m so proud of your progress. Celebrating these wins is just as important as working toward your goals.\n\nHow can we build on this momentum? What\'s the next step in your journey?`;
  }

  private getInquisitiveResponse(content: string, userContext: any): string {
    return `That\'s a great question! Let me help you think through this.\n\nBased on what you\'ve shared, I\'d suggest starting with some self-reflection. What does your intuition tell you? Sometimes the answers we seek are already within us.\n\nWould you like to explore this together step by step?`;
  }

  private getGoalSupportResponse(content: string, userContext: any): string {
    return `I love your commitment to growth! 🌱 The fact that you\'re thinking about improvement shows real self-awareness.\n\nLet\'s break this down together:\n1. What specifically do you want to achieve?\n2. Why is this important to you?\n3. What\'s one action you can take this week?\n\nI\'m here to support you every step of the way.`;
  }

  private getGeneralSupportResponse(content: string, userContext: any): string {
    return `Thank you for sharing that with me. I\'m here to support you on your personal growth journey.\n\nWhat\'s the most important thing you\'d like to focus on right now? Whether it\'s building new habits, overcoming challenges, or reaching specific goals, we can work on it together.`;
  }

  private getDefaultResponse(content: string, userContext: any): string {
    return `I\'m here to help you grow and achieve your potential! Whether you want to set new goals, build better habits, or overcome challenges, I\'m here to support you.\n\nWhat would you like to work on today?`;
  }

  private async getUserContext(userId: string): Promise<any> {
    try {
      // Get user's recent interactions and stored memories
      const recentGoals = await this.agentsService.getMemory(userId, 'recent_goals');
      const coachingHistory = await this.agentsService.getMemory(userId, 'coaching_history');
      const preferences = await this.agentsService.getMemory(userId, 'coaching_preferences');
      
      return {
        recentGoals: recentGoals?.value,
        history: coachingHistory?.value,
        preferences: preferences?.value,
        lastInteraction: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error(`Failed to get user context for ${userId}:`, error);
      return {};
    }
  }

  private async storeInteractionContext(
    userId: string,
    interactionType: string,
    content: string,
    response: string
  ) {
    try {
      // Store the interaction for future personalization
      await this.agentsService.storeMemory({
        userId,
        memoryType: 'interaction',
        key: `coaching_${Date.now()}`,
        value: {
          type: interactionType,
          userMessage: content.substring(0, 200),
          response: response.substring(0, 200),
          timestamp: new Date().toISOString(),
        },
        importance: this.getInteractionImportance(interactionType),
      });
    } catch (error) {
      this.logger.error('Failed to store interaction context:', error);
    }
  }

  private getInteractionImportance(interactionType: string): number {
    const importanceMap = {
      goal_setting: 8,
      check_in: 6,
      motivation: 4,
      greeting: 2,
      message: 5,
    };
    
    return importanceMap[interactionType as keyof typeof importanceMap] || 3;
  }

  async getCoachStats(userId?: string) {
    return {
      coachType: 'Personal Growth Coach',
      personality: this.coachPersonality,
      capabilities: [
        'Goal setting and planning',
        'Motivational support',
        'Habit formation guidance',
        'Progress tracking',
        'Overcoming obstacles',
        'Personal development advice'
      ],
      responseTypes: Object.keys(this.responseTemplates),
      totalTemplates: Object.values(this.responseTemplates).flat().length,
    };
  }
}