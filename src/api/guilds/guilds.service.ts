import { ForbiddenException, Inject, Injectable, Logger } from '@nestjs/common';
import { eq } from 'drizzle-orm';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { DATABASE_CONNECTION } from '../../core/database/database.service';
import { guilds, NewGuild } from '../../core/database/entities/guild.entity';
import * as schema from '../../core/database/schema';
import { DiscordService } from '../../discord/discord.service';
import { DiscordUtilsService } from '../../discord/utils/discord-utils.service';

@Injectable()
export class GuildsService {
  private readonly logger = new Logger(GuildsService.name);

  constructor(
    @Inject(DATABASE_CONNECTION) private db: NodePgDatabase<typeof schema>,
    private readonly discordService: DiscordService,
    private readonly discordUtils: DiscordUtilsService,
  ) {}

  async getGuildInfo(guildId: string, user: any) {
    try {
      // Check permissions
      await this.checkGuildPermissions(guildId, user.userId);

      // Get guild from Discord
      const discordGuild = await this.discordService.getGuildInfo(guildId);
      
      // Get guild from database
      const result = await this.db.select().from(guilds).where(eq(guilds.discordId, guildId)).limit(1);
      const dbGuild = result[0] || null;

      return {
        ...discordGuild,
        settings: dbGuild?.settings || {},
        features: dbGuild?.features || {},
        lastActivity: dbGuild?.lastActivityAt,
      };
    } catch (error) {
      this.logger.error(`Failed to get guild info for ${guildId}:`, error);
      throw error;
    }
  }

  async getEnabledFeatures(guildId: string, user: any) {
    try {
      await this.checkGuildPermissions(guildId, user.userId);
      
      const features = await this.discordService.getEnabledFeatures(guildId);
      return { guildId, features };
    } catch (error) {
      this.logger.error(`Failed to get features for guild ${guildId}:`, error);
      throw error;
    }
  }

  async updateFeature(guildId: string, feature: string, config: any, user: any) {
    try {
      await this.checkGuildPermissions(guildId, user.userId);

      // Update guild configuration in database
      const result = await this.db.select().from(guilds).where(eq(guilds.discordId, guildId)).limit(1);
      let guild = result[0] || null;

      if (!guild) {
        // Create guild entry if it doesn't exist
        const newGuild = {
          discordId: guildId,
          name: 'Unknown Guild',
          features: { [feature]: config },
          isActive: true,
        } as NewGuild;
        const insertResult = await this.db.insert(guilds).values(newGuild).returning();
        guild = insertResult[0];
      } else {
        const updatedFeatures = {
          ...guild.features,
          [feature]: config,
        };
        await this.db.update(guilds).set({ features: updatedFeatures } as any).where(eq(guilds.discordId, guildId));
        guild = { ...guild, features: updatedFeatures };
      }

      return {
        guildId,
        feature,
        config,
        message: 'Feature updated successfully',
      };
    } catch (error) {
      this.logger.error(`Failed to update feature ${feature} for guild ${guildId}:`, error);
      throw error;
    }
  }

  async getChannels(guildId: string, user: any) {
    try {
      await this.checkGuildPermissions(guildId, user.userId);
      return await this.discordUtils.getGuildChannels(guildId);
    } catch (error) {
      this.logger.error(`Failed to get channels for guild ${guildId}:`, error);
      throw error;
    }
  }

  async getRoles(guildId: string, user: any) {
    try {
      await this.checkGuildPermissions(guildId, user.userId);
      return await this.discordUtils.getGuildRoles(guildId);
    } catch (error) {
      this.logger.error(`Failed to get roles for guild ${guildId}:`, error);
      throw error;
    }
  }

  async getMembers(guildId: string, user: any) {
    try {
      await this.checkGuildPermissions(guildId, user.userId);
      
      // For security, return limited member info
      return {
        guildId,
        memberCount: await this.getMemberCount(guildId),
        message: 'Full member list requires additional permissions',
      };
    } catch (error) {
      this.logger.error(`Failed to get members for guild ${guildId}:`, error);
      throw error;
    }
  }

  async getStats(guildId: string, user: any) {
    try {
      await this.checkGuildPermissions(guildId, user.userId);

      const result = await this.db.select().from(guilds).where(eq(guilds.discordId, guildId)).limit(1);
      const guild = result[0] || null;

      const discordGuild = await this.discordService.getGuildInfo(guildId);
      
      return {
        guildId,
        memberCount: discordGuild.memberCount,
        features: Object.keys(guild?.features || {}),
        lastActivity: guild?.lastActivityAt,
        isActive: guild?.isActive || false,
      };
    } catch (error) {
      this.logger.error(`Failed to get stats for guild ${guildId}:`, error);
      throw error;
    }
  }

  private async checkGuildPermissions(guildId: string, userId: string) {
    try {
      const hasPermissions = await this.discordUtils.checkBotGuildPermissions(userId, guildId);
      if (!hasPermissions) {
        throw new ForbiddenException('Insufficient permissions to access this guild');
      }
    } catch (error) {
      if (error instanceof ForbiddenException) {
        throw error;
      }
      this.logger.error(`Permission check failed for user ${userId} in guild ${guildId}:`, error);
      throw new ForbiddenException('Unable to verify guild permissions');
    }
  }

  private async getMemberCount(guildId: string): Promise<number> {
    try {
      const guildInfo = await this.discordService.getGuildInfo(guildId);
      return guildInfo.memberCount || 0;
    } catch (error) {
      this.logger.error(`Failed to get member count for guild ${guildId}:`, error);
      return 0;
    }
  }
}