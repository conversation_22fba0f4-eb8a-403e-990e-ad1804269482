import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Param,
  Body,
  UseGuards,
  Req,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { Request } from 'express';
import { GuildsService } from './guilds.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('guilds')
@Controller('guilds')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class GuildsController {
  constructor(private readonly guildsService: GuildsService) {}

  @Get(':guildId')
  @ApiOperation({ summary: 'Get guild information' })
  @ApiResponse({ status: 200, description: 'Guild information retrieved' })
  async getGuild(@Param('guildId') guildId: string, @Req() req: Request) {
    return await this.guildsService.getGuildInfo(guildId, req.user);
  }

  @Get(':guildId/features')
  @ApiOperation({ summary: 'Get enabled features for guild' })
  @ApiResponse({ status: 200, description: 'Guild features retrieved' })
  async getGuildFeatures(@Param('guildId') guildId: string, @Req() req: Request) {
    return await this.guildsService.getEnabledFeatures(guildId, req.user);
  }

  @Put(':guildId/features/:feature')
  @ApiOperation({ summary: 'Update guild feature configuration' })
  @ApiResponse({ status: 200, description: 'Feature updated successfully' })
  async updateFeature(
    @Param('guildId') guildId: string,
    @Param('feature') feature: string,
    @Body() config: any,
    @Req() req: Request,
  ) {
    return await this.guildsService.updateFeature(guildId, feature, config, req.user);
  }

  @Get(':guildId/channels')
  @ApiOperation({ summary: 'Get guild channels' })
  @ApiResponse({ status: 200, description: 'Guild channels retrieved' })
  async getChannels(@Param('guildId') guildId: string, @Req() req: Request) {
    return await this.guildsService.getChannels(guildId, req.user);
  }

  @Get(':guildId/roles')
  @ApiOperation({ summary: 'Get guild roles' })
  @ApiResponse({ status: 200, description: 'Guild roles retrieved' })
  async getRoles(@Param('guildId') guildId: string, @Req() req: Request) {
    return await this.guildsService.getRoles(guildId, req.user);
  }

  @Get(':guildId/members')
  @ApiOperation({ summary: 'Get guild members' })
  @ApiResponse({ status: 200, description: 'Guild members retrieved' })
  async getMembers(@Param('guildId') guildId: string, @Req() req: Request) {
    return await this.guildsService.getMembers(guildId, req.user);
  }

  @Get(':guildId/stats')
  @ApiOperation({ summary: 'Get guild statistics' })
  @ApiResponse({ status: 200, description: 'Guild statistics retrieved' })
  async getStats(@Param('guildId') guildId: string, @Req() req: Request) {
    return await this.guildsService.getStats(guildId, req.user);
  }
}