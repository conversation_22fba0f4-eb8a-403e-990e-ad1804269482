import { Module } from '@nestjs/common';
import { GuildsController } from './guilds.controller';
import { GuildsService } from './guilds.service';
import { DatabaseModule } from '../../core/database/database.module';
import { DiscordModule } from '../../discord/discord.module';
import { SecurityModule } from '../../core/security/security.module';

@Module({
  imports: [DatabaseModule, DiscordModule, SecurityModule],
  controllers: [GuildsController],
  providers: [GuildsService],
  exports: [GuildsService],
})
export class GuildsModule {}