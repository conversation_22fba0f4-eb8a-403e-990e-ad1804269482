import { Injectable, Logger } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { SessionService } from '../../core/security/session.service';
import { UserService } from '../../core/security/user.service';
import { EncryptionService } from '../../core/security/encryption.service';
import { Request } from 'express';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    private readonly jwtService: JwtService,
    private readonly sessionService: SessionService,
    private readonly userService: UserService,
    private readonly encryptionService: EncryptionService,
  ) {}

  async login(discordUser: any, req: Request) {
    try {
      // Create or update user
      const user = await this.userService.upsertUser({
        discordId: discordUser.discordId,
        username: discordUser.username,
        email: discordUser.email,
      });

      // Create session
      const session = await this.sessionService.createSession(user.discordId, req);

      // Generate JWT token
      const payload = { 
        sub: user.discordId, 
        username: user.username,
        sessionId: session.sessionId,
      };
      const accessToken = this.jwtService.sign(payload);

      return {
        accessToken,
        sessionToken: session.sessionId,
        user: {
          id: user.discordId,
          username: user.username,
          email: user.email,
        },
      };
    } catch (error) {
      this.logger.error('Login failed:', error);
      throw new Error('Authentication failed');
    }
  }

  async validateSession(sessionToken: string, req: Request) {
    return await this.sessionService.validateSession(sessionToken, req);
  }

  async logout(sessionToken: string) {
    await this.sessionService.revokeSession(sessionToken);
  }

  async generateCsrfToken(): Promise<string> {
    return this.encryptionService.generateCSRFToken();
  }

  async validateCsrfToken(token: string): Promise<boolean> {
    return this.encryptionService.validateCSRFToken(token);
  }
}