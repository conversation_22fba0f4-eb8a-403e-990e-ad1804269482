import {
  Controller,
  Get,
  Post,
  Delete,
  Req,
  Res,
  UseGuards,
  Body,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { Request, Response } from 'express';
import { AuthGuard } from '@nestjs/passport';
import { AuthService } from './auth.service';
import { JwtAuthGuard } from './guards/jwt-auth.guard';

@ApiTags('auth')
@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Get('login')
  @ApiOperation({ summary: 'Initiate Discord OAuth login' })
  @ApiResponse({ status: 302, description: 'Redirect to Discord OAuth' })
  @UseGuards(AuthGuard('discord'))
  async login() {
    // This route is handled by the Discord strategy
  }

  @Get('callback')
  @ApiOperation({ summary: 'Discord OAuth callback' })
  @ApiResponse({ status: 200, description: 'Authentication successful' })
  @ApiResponse({ status: 401, description: 'Authentication failed' })
  @UseGuards(AuthGuard('discord'))
  async callback(@Req() req: Request, @Res() res: Response) {
    try {
      const user = req.user as any;
      const result = await this.authService.login(user, req);
      
      // Set secure HTTP-only cookie
      res.cookie('session_token', result.sessionToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 24 * 60 * 60 * 1000, // 24 hours
      });
      
      // Redirect to frontend with success
      const frontendUrl = process.env.WEB_URL || 'http://localhost:3000';
      res.redirect(`${frontendUrl}/auth/success`);
    } catch (error) {
      const frontendUrl = process.env.WEB_URL || 'http://localhost:3000';
      res.redirect(`${frontendUrl}/auth/error`);
    }
  }

  @Get('session')
  @ApiOperation({ summary: 'Get current session information' })
  @ApiResponse({ status: 200, description: 'Session information' })
  @ApiResponse({ status: 401, description: 'Not authenticated' })
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  async getSession(@Req() req: Request) {
    const sessionToken = req.cookies?.session_token;
    if (!sessionToken) {
      return { authenticated: false };
    }
    
    const session = await this.authService.validateSession(sessionToken, req);
    return {
      authenticated: !!session,
      user: session?.user || null,
      session: session ? {
        id: session.sessionId,
        expiresAt: session.expiresAt,
        lastAccessedAt: session.lastAccessedAt,
      } : null,
    };
  }

  @Post('signout')
  @ApiOperation({ summary: 'Sign out and invalidate session' })
  @ApiResponse({ status: 200, description: 'Signed out successfully' })
  async signout(@Req() req: Request, @Res() res: Response) {
    const sessionToken = req.cookies?.session_token;
    
    if (sessionToken) {
      await this.authService.logout(sessionToken);
    }
    
    // Clear the session cookie
    res.clearCookie('session_token');
    
    return res.status(HttpStatus.OK).json({ message: 'Signed out successfully' });
  }

  @Get('csrf-token')
  @ApiOperation({ summary: 'Get CSRF token' })
  @ApiResponse({ status: 200, description: 'CSRF token' })
  async getCsrfToken() {
    const token = await this.authService.generateCsrfToken();
    return { csrfToken: token };
  }

  @Get('test')
  @ApiOperation({ summary: 'Test authentication endpoint' })
  @ApiResponse({ status: 200, description: 'Authentication test result' })
  async testAuth(@Req() req: Request) {
    const sessionToken = req.cookies?.session_token;
    const userAgent = req.get('User-Agent');
    const ipAddress = req.ip || req.connection.remoteAddress;
    
    return {
      hasSessionToken: !!sessionToken,
      userAgent,
      ipAddress,
      headers: req.headers,
      cookies: req.cookies,
    };
  }

  @Get('health')
  @ApiOperation({ summary: 'Authentication service health check' })
  @ApiResponse({ status: 200, description: 'Auth service health' })
  async healthCheck() {
    return {
      status: 'ok',
      service: 'auth',
      timestamp: new Date().toISOString(),
      discord_configured: !!process.env.DISCORD_CLIENT_ID,
      jwt_configured: !!process.env.JWT_SECRET,
    };
  }
}