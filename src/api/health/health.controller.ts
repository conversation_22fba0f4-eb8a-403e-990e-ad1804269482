import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { DiscordService } from '../../discord/discord.service';
import { DatabaseService } from '../../core/database/database.service';

@ApiTags('health')
@Controller('health')
export class HealthController {
  constructor(
    private discordService: DiscordService,
    private databaseService: DatabaseService,
  ) {}

  @Get()
  @ApiOperation({ summary: 'Get application health status' })
  @ApiResponse({ status: 200, description: 'Health check results' })
  async check() {
    const dbHealthy = await this.databaseService.healthCheck();
    const discordHealth = await this.discordService.healthCheck();
    
    return {
      status: dbHealthy && discordHealth.status === 'healthy' ? 'ok' : 'error',
      timestamp: new Date().toISOString(),
      info: {
        database: { status: dbHealthy ? 'up' : 'down' },
        discord: discordHealth,
      },
      details: {
        database: { status: dbHealthy ? 'up' : 'down' },
        discord: discordHealth,
      },
    };
  }

  @Get('simple')
  @ApiOperation({ summary: 'Simple health check endpoint' })
  @ApiResponse({ status: 200, description: 'Simple health status' })
  async simpleCheck() {
    const dbHealthy = await this.databaseService.healthCheck();
    const discordHealth = await this.discordService.healthCheck();
    
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      service: 'discord-bot-nestjs',
      version: '1.0.0',
      uptime: process.uptime(),
      environment: process.env.NODE_ENV || 'development',
      port: process.env.PORT || '8080',
      database: dbHealthy ? 'configured' : 'error',
      discord_token: process.env.DISCORD_TOKEN ? 'configured' : 'missing',
      discord_status: discordHealth.status,
      discord_guilds: discordHealth.guilds,
    };
  }
}