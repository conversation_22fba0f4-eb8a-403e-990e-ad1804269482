import { Injectable, Logger, ForbiddenException, Inject } from '@nestjs/common';
import { DATABASE_CONNECTION } from '../../core/database/database.service';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { eq, and, gte, desc } from 'drizzle-orm';
import { sessions, users, guilds, Session, User, Guild } from '../../core/database/schema';
import { SessionService } from '../../core/security/session.service';
import { DiscordService } from '../../discord/discord.service';

@Injectable()
export class AdminService {
  private readonly logger = new Logger(AdminService.name);
  private readonly adminUserIds = process.env.ADMIN_USER_IDS?.split(',') || [];

  constructor(
    @Inject(DATABASE_CONNECTION) private readonly db: NodePgDatabase,
    private readonly sessionService: SessionService,
    private readonly discordService: DiscordService,
  ) {}

  async getActiveSessions(user: any) {
    this.checkAdminPermissions(user);
    
    try {
      const sessionResults = await this.db
        .select({
          sessionId: sessions.sessionId,
          userId: sessions.userId,
          ipAddress: sessions.ipAddress,
          userAgent: sessions.userAgent,
          createdAt: sessions.createdAt,
          lastAccessedAt: sessions.lastAccessedAt,
          expiresAt: sessions.expiresAt,
          username: users.username,
        })
        .from(sessions)
        .leftJoin(users, eq(sessions.userId, users.discordId))
        .where(eq(sessions.isRevoked, false))
        .orderBy(desc(sessions.lastAccessedAt))
        .limit(100);

      return {
        total: sessionResults.length,
        sessions: sessionResults.map(session => ({
          sessionId: session.sessionId,
          userId: session.userId,
          username: session.username || 'Unknown',
          ipAddress: session.ipAddress,
          userAgent: session.userAgent,
          createdAt: session.createdAt,
          lastAccessedAt: session.lastAccessedAt,
          expiresAt: session.expiresAt,
        })),
      };
    } catch (error) {
      this.logger.error('Failed to get active sessions:', error);
      throw error;
    }
  }

  async getUserStats(user: any) {
    this.checkAdminPermissions(user);
    
    try {
      const [totalUsersResult, activeUsersResult, recentUsersResult] = await Promise.all([
        this.db.select().from(users),
        this.db.select().from(users).where(eq(users.isActive, true)),
        this.db.select().from(users).where(gte(users.createdAt, new Date(Date.now() - 30 * 24 * 60 * 60 * 1000))),
      ]);
      
      const totalUsers = totalUsersResult.length;
      const activeUsers = activeUsersResult.length;
      const recentUsers = recentUsersResult.length;

      return {
        totalUsers,
        activeUsers,
        inactiveUsers: totalUsers - activeUsers,
        recentUsers,
        userGrowthRate: totalUsers > 0 ? (recentUsers / totalUsers) * 100 : 0,
      };
    } catch (error) {
      this.logger.error('Failed to get user stats:', error);
      throw error;
    }
  }

  async getGuildStats(user: any) {
    this.checkAdminPermissions(user);
    
    try {
      const [totalGuildsResult, activeGuildsResult] = await Promise.all([
        this.db.select().from(guilds),
        this.db.select().from(guilds).where(eq(guilds.isActive, true)),
      ]);
      
      const totalGuilds = totalGuildsResult.length;
      const activeGuilds = activeGuildsResult.length;
      const discordGuilds = this.discordService.getGuildCount();

      return {
        totalGuilds,
        activeGuilds,
        inactiveGuilds: totalGuilds - activeGuilds,
        discordGuilds,
        syncStatus: discordGuilds === activeGuilds ? 'synced' : 'out_of_sync',
      };
    } catch (error) {
      this.logger.error('Failed to get guild stats:', error);
      throw error;
    }
  }

  async getSystemHealth(user: any) {
    this.checkAdminPermissions(user);
    
    try {
      const discordHealth = await this.discordService.healthCheck();
      const uptime = process.uptime();
      const memoryUsage = process.memoryUsage();

      return {
        discord: discordHealth,
        system: {
          uptime: Math.floor(uptime),
          uptimeFormatted: this.formatUptime(uptime),
          memory: {
            used: Math.round(memoryUsage.heapUsed / 1024 / 1024),
            total: Math.round(memoryUsage.heapTotal / 1024 / 1024),
            external: Math.round(memoryUsage.external / 1024 / 1024),
          },
          nodeVersion: process.version,
          platform: process.platform,
        },
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('Failed to get system health:', error);
      throw error;
    }
  }

  async revokeSession(sessionId: string, user: any) {
    this.checkAdminPermissions(user);
    
    try {
      await this.sessionService.revokeSession(sessionId);
      this.logger.log(`Session ${sessionId} revoked by admin ${user.userId}`);
      
      return {
        message: 'Session revoked successfully',
        sessionId,
        revokedBy: user.userId,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error(`Failed to revoke session ${sessionId}:`, error);
      throw error;
    }
  }

  async cleanupExpiredSessions(user: any) {
    this.checkAdminPermissions(user);
    
    try {
      const cleaned = await this.sessionService.cleanupExpiredSessions();
      this.logger.log(`${cleaned} expired sessions cleaned up by admin ${user.userId}`);
      
      return {
        message: 'Expired sessions cleaned up',
        sessionsRemoved: cleaned,
        cleanedBy: user.userId,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('Failed to cleanup expired sessions:', error);
      throw error;
    }
  }

  async getSystemLogs(user: any) {
    this.checkAdminPermissions(user);
    
    try {
      // In a real implementation, you would read from log files
      // For now, return basic log information
      return {
        message: 'Log access available',
        logFiles: [
          { name: 'application.log', size: '1.2MB', lastModified: new Date() },
          { name: 'error.log', size: '245KB', lastModified: new Date() },
          { name: 'discord.log', size: '512KB', lastModified: new Date() },
        ],
        note: 'Full log streaming requires additional implementation',
      };
    } catch (error) {
      this.logger.error('Failed to get system logs:', error);
      throw error;
    }
  }

  private checkAdminPermissions(user: any) {
    if (!user || !this.adminUserIds.includes(user.userId)) {
      throw new ForbiddenException('Admin permissions required');
    }
  }

  private formatUptime(uptime: number): string {
    const hours = Math.floor(uptime / 3600);
    const minutes = Math.floor((uptime % 3600) / 60);
    const seconds = Math.floor(uptime % 60);
    return `${hours}h ${minutes}m ${seconds}s`;
  }
}