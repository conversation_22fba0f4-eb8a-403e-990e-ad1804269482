import { Inject, Injectable, Logger } from '@nestjs/common';
import { count, eq, gte, sql } from 'drizzle-orm';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { DATABASE_CONNECTION } from '../../../core/database/database.service';
import { agentInteractions, guilds, sessions, users } from '../../../core/database/schema';

@Injectable()
export class AnalyticsService {
  private readonly logger = new Logger(AnalyticsService.name);

  constructor(
    @Inject(DATABASE_CONNECTION)
    private readonly db: NodePgDatabase,
  ) {}

  async getDashboardStats() {
    try {
      const now = new Date();
      const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

      const [
        totalUsers,
        activeUsers,
        newUsersToday,
        newUsersWeek,
        totalGuilds,
        activeGuilds,
        totalSessions,
        activeSessions,
        totalInteractions,
        interactionsToday
      ] = await Promise.all([
        this.db.select({ count: count() }).from(users).then(r => r[0]?.count || 0),
        this.db.select({ count: count() }).from(users).where(eq(users.isActive, true)).then(r => r[0]?.count || 0),
        this.db.select({ count: count() }).from(users).where(gte(users.createdAt, oneDayAgo)).then(r => r[0]?.count || 0),
        this.db.select({ count: count() }).from(users).where(gte(users.createdAt, oneWeekAgo)).then(r => r[0]?.count || 0),
        this.db.select({ count: count() }).from(guilds).then(r => r[0]?.count || 0),
        this.db.select({ count: count() }).from(guilds).where(eq(guilds.isActive, true)).then(r => r[0]?.count || 0),
        this.db.select({ count: count() }).from(sessions).then(r => r[0]?.count || 0),
        this.db.select({ count: count() }).from(sessions).where(eq(sessions.isRevoked, false)).then(r => r[0]?.count || 0),
        this.db.select({ count: count() }).from(agentInteractions).then(r => r[0]?.count || 0),
        this.db.select({ count: count() }).from(agentInteractions).where(gte(agentInteractions.createdAt, oneDayAgo)).then(r => r[0]?.count || 0),
      ]);

      const userGrowthRate = totalUsers > 0 ? ((newUsersWeek / totalUsers) * 100) : 0;
      const activityRate = totalUsers > 0 ? ((activeUsers / totalUsers) * 100) : 0;

      return {
        overview: {
          totalUsers,
          activeUsers,
          totalGuilds,
          activeGuilds,
          activeSessions,
          totalInteractions,
        },
        growth: {
          newUsersToday,
          newUsersWeek,
          userGrowthRate: Math.round(userGrowthRate * 100) / 100,
          activityRate: Math.round(activityRate * 100) / 100,
        },
        engagement: {
          interactionsToday,
          averageInteractionsPerUser: totalUsers > 0 ? Math.round(totalInteractions / totalUsers) : 0,
          sessionUtilization: Math.round((activeSessions / Math.max(totalUsers, 1)) * 100),
        },
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('Failed to get dashboard stats:', error);
      throw error;
    }
  }

  async getUserAnalytics() {
    try {
      const now = new Date();
      const periods = {
        today: new Date(now.getTime() - 24 * 60 * 60 * 1000),
        week: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000),
        month: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000),
      };

      const analytics = await Promise.all([
        // Registration trends
        this.getUserRegistrationTrend(periods),
        // Activity patterns
        this.getUserActivityPatterns(),
        // Geographic distribution (if available)
        this.getUserDistribution(),
        // Top users by activity
        this.getTopActiveUsers(),
      ]);

      return {
        registrationTrend: analytics[0],
        activityPatterns: analytics[1],
        distribution: analytics[2],
        topUsers: analytics[3],
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('Failed to get user analytics:', error);
      throw error;
    }
  }

  async getGuildAnalytics() {
    try {
      const [
        guildSizes,
        featureUsage,
        guildActivity,
        topGuilds
      ] = await Promise.all([
        this.getGuildSizeDistribution(),
        this.getFeatureUsageStats(),
        this.getGuildActivityStats(),
        this.getTopGuildsByActivity(),
      ]);

      return {
        sizeDistribution: guildSizes,
        featureUsage,
        activity: guildActivity,
        topGuilds,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('Failed to get guild analytics:', error);
      throw error;
    }
  }

  async getAgentAnalytics() {
    try {
      const now = new Date();
      const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

      const [
        totalInteractions,
        interactionsToday,
        interactionsWeek,
        agentBreakdown,
        interactionTypes
      ] = await Promise.all([
        this.db.select({ count: count() }).from(agentInteractions).then(r => r[0]?.count || 0),
        this.db.select({ count: count() }).from(agentInteractions).where(gte(agentInteractions.createdAt, oneDayAgo)).then(r => r[0]?.count || 0),
        this.db.select({ count: count() }).from(agentInteractions).where(gte(agentInteractions.createdAt, oneWeekAgo)).then(r => r[0]?.count || 0),
        this.getAgentInteractionBreakdown(),
        this.getInteractionTypeBreakdown(),
      ]);

      return {
        overview: {
          totalInteractions,
          interactionsToday,
          interactionsWeek,
          averageDaily: Math.round(interactionsWeek / 7),
        },
        breakdown: {
          byAgent: agentBreakdown,
          byType: interactionTypes,
        },
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('Failed to get agent analytics:', error);
      throw error;
    }
  }

  private async getUserRegistrationTrend(periods: any) {
    // In a real implementation, this would query daily registration counts
    return {
      today: await this.db.select({ count: count() }).from(users).where(gte(users.createdAt, periods.today)).then(r => r[0]?.count || 0),
      week: await this.db.select({ count: count() }).from(users).where(gte(users.createdAt, periods.week)).then(r => r[0]?.count || 0),
      month: await this.db.select({ count: count() }).from(users).where(gte(users.createdAt, periods.month)).then(r => r[0]?.count || 0),
    };
  }

  private async getUserActivityPatterns() {
    const activeUsers = await this.db.select({ count: count() }).from(users).where(eq(users.isActive, true)).then(r => r[0]?.count || 0);
    const totalUsers = await this.db.select({ count: count() }).from(users).then(r => r[0]?.count || 0);

    return {
      activeUsers,
      inactiveUsers: totalUsers - activeUsers,
      activityRate: totalUsers > 0 ? Math.round((activeUsers / totalUsers) * 100) : 0,
    };
  }

  private async getUserDistribution() {
    // Placeholder - would need actual geographic data
    return {
      regions: [
        { name: 'North America', users: 45, percentage: 45 },
        { name: 'Europe', users: 30, percentage: 30 },
        { name: 'Asia', users: 20, percentage: 20 },
        { name: 'Other', users: 5, percentage: 5 },
      ],
    };
  }

  private async getTopActiveUsers() {
    // This would typically join with interaction data
    const usersList = await this.db.select()
      .from(users)
      .where(eq(users.isActive, true))
      .orderBy(sql`${users.lastActivityAt} DESC NULLS LAST`)
      .limit(10);

    return usersList.map(user => ({
      id: user.id,
      username: user.username,
      lastActivity: user.lastActivityAt,
      // interactionCount would come from a join
      interactionCount: Math.floor(Math.random() * 100), // Placeholder
    }));
  }

  private async getGuildSizeDistribution() {
    const guildsList = await this.db.select().from(guilds);

    // Simulate guild size distribution
    const distribution = {
      small: guildsList.filter(g => Math.random() < 0.6).length, // < 100 members
      medium: guildsList.filter(g => Math.random() < 0.3).length, // 100-1000 members
      large: guildsList.filter(g => Math.random() < 0.1).length, // > 1000 members
    };

    return distribution;
  }

  private async getFeatureUsageStats() {
    // Would analyze which features are most used across guilds
    return {
      welcome: { enabled: 85, usage: 92 },
      moderation: { enabled: 78, usage: 65 },
      music: { enabled: 95, usage: 88 },
      economy: { enabled: 60, usage: 45 },
      starboard: { enabled: 40, usage: 35 },
      aiAgents: { enabled: 25, usage: 80 },
    };
  }

  private async getGuildActivityStats() {
    const totalGuilds = await this.db.select({ count: count() }).from(guilds).then(r => r[0]?.count || 0);
    const activeGuilds = await this.db.select({ count: count() }).from(guilds).where(eq(guilds.isActive, true)).then(r => r[0]?.count || 0);

    return {
      totalGuilds,
      activeGuilds,
      inactiveGuilds: totalGuilds - activeGuilds,
      activityRate: totalGuilds > 0 ? Math.round((activeGuilds / totalGuilds) * 100) : 0,
    };
  }

  private async getTopGuildsByActivity() {
    const guildsList = await this.db.select()
      .from(guilds)
      .where(eq(guilds.isActive, true))
      .orderBy(sql`${guilds.lastActivityAt} DESC NULLS LAST`)
      .limit(10);

    return guildsList.map(guild => ({
      id: guild.id,
      name: guild.name,
      lastActivity: guild.lastActivityAt,
      // memberCount would come from Discord API or cached data
      memberCount: Math.floor(Math.random() * 1000) + 50, // Placeholder
    }));
  }

  private async getAgentInteractionBreakdown() {
    // This would group interactions by agent type
    return [
      { agent: 'personal_growth_coach', count: 450, percentage: 45 },
      { agent: 'intake_specialist', count: 300, percentage: 30 },
      { agent: 'progress_tracker', count: 200, percentage: 20 },
      { agent: 'general', count: 50, percentage: 5 },
    ];
  }

  private async getInteractionTypeBreakdown() {
    // This would group interactions by type
    return [
      { type: 'message', count: 600, percentage: 60 },
      { type: 'command', count: 250, percentage: 25 },
      { type: 'assessment', count: 100, percentage: 10 },
      { type: 'check_in', count: 50, percentage: 5 },
    ];
  }
}