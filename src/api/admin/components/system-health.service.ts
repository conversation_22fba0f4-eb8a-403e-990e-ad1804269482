import { Inject, Injectable, Logger } from '@nestjs/common';
import { count, eq, gte } from 'drizzle-orm';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { DATABASE_CONNECTION } from '../../../core/database/database.service';
import { guilds, sessions, users } from '../../../core/database/schema';

@Injectable()
export class SystemHealthService {
  private readonly logger = new Logger(SystemHealthService.name);

  constructor(
    @Inject(DATABASE_CONNECTION)
    private readonly db: NodePgDatabase,
  ) {}

  async getSystemHealth() {
    try {
      const [
        totalUsers,
        activeUsers,
        totalGuilds,
        activeSessions,
        memoryUsage,
        uptime
      ] = await Promise.all([
        this.db.select({ count: count() }).from(users).then(r => r[0]?.count || 0),
        this.db.select({ count: count() }).from(users).where(eq(users.isActive, true)).then(r => r[0]?.count || 0),
        this.db.select({ count: count() }).from(guilds).then(r => r[0]?.count || 0),
        this.db.select({ count: count() }).from(sessions).where(eq(sessions.isRevoked, false)).then(r => r[0]?.count || 0),
        this.getMemoryUsage(),
        process.uptime()
      ]);

      const healthStatus = this.calculateHealthStatus({
        totalUsers,
        activeUsers,
        totalGuilds,
        activeSessions,
        memoryUsage,
        uptime
      });

      return {
        status: healthStatus.overall,
        timestamp: new Date().toISOString(),
        uptime: {
          seconds: Math.floor(uptime),
          formatted: this.formatUptime(uptime),
        },
        database: {
          status: 'healthy',
          totalUsers,
          activeUsers,
          totalGuilds,
          activeSessions,
        },
        system: {
          status: healthStatus.system,
          memory: memoryUsage,
          nodeVersion: process.version,
          platform: process.platform,
        },
        services: {
          discord: healthStatus.discord,
          database: healthStatus.database,
          api: healthStatus.api,
        },
        alerts: await this.getActiveAlerts(),
      };
    } catch (error) {
      this.logger.error('Failed to get system health:', error);
      return {
        status: 'critical',
        error: 'Failed to retrieve system health',
        timestamp: new Date().toISOString(),
      };
    }
  }

  async getSystemMetrics() {
    try {
      const now = new Date();
      const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
      const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

      const [
        recentUsers,
        recentSessions,
        totalRequests,
        memoryTrend
      ] = await Promise.all([
        this.db.select({ count: count() }).from(users).where(gte(users.createdAt, oneHourAgo)).then(r => r[0]?.count || 0),
        this.db.select({ count: count() }).from(sessions).where(gte(sessions.createdAt, oneHourAgo)).then(r => r[0]?.count || 0),
        this.getRequestCount(),
        this.getMemoryTrend()
      ]);

      return {
        timestamp: new Date().toISOString(),
        metrics: {
          users: {
            newLastHour: recentUsers,
            newLast24h: await this.db.select({ count: count() }).from(users).where(gte(users.createdAt, oneDayAgo)).then(r => r[0]?.count || 0),
          },
          sessions: {
            newLastHour: recentSessions,
            activeTotal: await this.db.select({ count: count() }).from(sessions).where(eq(sessions.isRevoked, false)).then(r => r[0]?.count || 0),
          },
          requests: {
            total: totalRequests,
            averagePerMinute: Math.round(totalRequests / (process.uptime() / 60)),
          },
          memory: memoryTrend,
        },
      };
    } catch (error) {
      this.logger.error('Failed to get system metrics:', error);
      throw error;
    }
  }

  async getPerformanceMetrics() {
    const memoryUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();

    return {
      timestamp: new Date().toISOString(),
      performance: {
        memory: {
          heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024),
          heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024),
          external: Math.round(memoryUsage.external / 1024 / 1024),
          rss: Math.round(memoryUsage.rss / 1024 / 1024),
          utilization: Math.round((memoryUsage.heapUsed / memoryUsage.heapTotal) * 100),
        },
        cpu: {
          user: cpuUsage.user,
          system: cpuUsage.system,
        },
        uptime: {
          process: Math.floor(process.uptime()),
          system: Math.floor(require('os').uptime()),
        },
        eventLoop: {
          delay: await this.getEventLoopDelay(),
        },
      },
    };
  }

  async getActiveAlerts() {
    const alerts = [];
    const memoryUsage = this.getMemoryUsage();
    const uptime = process.uptime();

    // Memory usage alert
    if (memoryUsage.utilization > 85) {
      alerts.push({
        id: 'high-memory-usage',
        level: memoryUsage.utilization > 95 ? 'critical' : 'warning',
        title: 'High Memory Usage',
        message: `Memory usage is at ${memoryUsage.utilization}%`,
        timestamp: new Date().toISOString(),
      });
    }

    // Low uptime alert (less than 5 minutes)
    if (uptime < 300) {
      alerts.push({
        id: 'recent-restart',
        level: 'info',
        title: 'Recent System Restart',
        message: `System has been running for only ${Math.floor(uptime)} seconds`,
        timestamp: new Date().toISOString(),
      });
    }

    // Database connection health (simplified check)
    try {
      await this.db.select({ count: count() }).from(users);
    } catch (error) {
      alerts.push({
        id: 'database-connection',
        level: 'critical',
        title: 'Database Connection Issue',
        message: 'Unable to connect to database',
        timestamp: new Date().toISOString(),
      });
    }

    return alerts;
  }

  private calculateHealthStatus(metrics: any) {
    let overallScore = 100;
    const issues = [];

    // Memory usage check
    if (metrics.memoryUsage.utilization > 90) {
      overallScore -= 30;
      issues.push('High memory usage');
    } else if (metrics.memoryUsage.utilization > 80) {
      overallScore -= 15;
      issues.push('Elevated memory usage');
    }

    // User activity check
    const userActivityRatio = metrics.activeUsers / Math.max(metrics.totalUsers, 1);
    if (userActivityRatio < 0.1) {
      overallScore -= 10;
      issues.push('Low user activity');
    }

    // Determine overall status
    let overall = 'healthy';
    let system = 'healthy';
    let discord = 'healthy';
    let database = 'healthy';
    let api = 'healthy';

    if (overallScore < 50) {
      overall = 'critical';
      system = 'critical';
    } else if (overallScore < 75) {
      overall = 'degraded';
      system = 'degraded';
    }

    return {
      overall,
      system,
      discord,
      database,
      api,
      score: overallScore,
      issues,
    };
  }

  private getMemoryUsage() {
    const usage = process.memoryUsage();
    return {
      heapUsed: Math.round(usage.heapUsed / 1024 / 1024),
      heapTotal: Math.round(usage.heapTotal / 1024 / 1024),
      external: Math.round(usage.external / 1024 / 1024),
      rss: Math.round(usage.rss / 1024 / 1024),
      utilization: Math.round((usage.heapUsed / usage.heapTotal) * 100),
    };
  }

  private formatUptime(uptime: number): string {
    const hours = Math.floor(uptime / 3600);
    const minutes = Math.floor((uptime % 3600) / 60);
    const seconds = Math.floor(uptime % 60);
    return `${hours}h ${minutes}m ${seconds}s`;
  }

  private async getRequestCount(): Promise<number> {
    // This would typically come from request logging middleware
    // For now, return a simulated count
    return Math.floor(process.uptime() * 10); // ~10 requests per second average
  }

  private async getMemoryTrend() {
    const current = this.getMemoryUsage();
    // In a real implementation, this would track memory usage over time
    return {
      current: current.heapUsed,
      trend: 'stable', // 'increasing', 'decreasing', 'stable'
      change: 0, // MB change from previous measurement
    };
  }

  private async getEventLoopDelay(): Promise<number> {
    return new Promise((resolve) => {
      const start = process.hrtime.bigint();
      setImmediate(() => {
        const delta = process.hrtime.bigint() - start;
        resolve(Number(delta) / 1000000); // Convert to milliseconds
      });
    });
  }
}