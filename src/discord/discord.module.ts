import { Module } from '@nestjs/common';
import { NecordModule } from 'necord';
import { DiscordService } from './discord.service';
import { DiscordEventsService } from './events/discord-events.service';
import { DiscordCommandsService } from './commands/discord-commands.service';
import { DiscordUtilsService } from './utils/discord-utils.service';
import { SecurityModule } from '../core/security/security.module';
import { DatabaseModule } from '../core/database/database.module';

@Module({
  imports: [SecurityModule, DatabaseModule],
  providers: [
    DiscordService,
    DiscordEventsService,
    DiscordCommandsService,
    DiscordUtilsService,
  ],
  exports: [DiscordService, DiscordUtilsService],
})
export class DiscordModule {}