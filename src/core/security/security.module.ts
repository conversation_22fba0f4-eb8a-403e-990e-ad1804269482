import { Module } from '@nestjs/common';
import { EncryptionService } from './encryption.service';
import { SessionService } from './session.service';
import { UserService } from './user.service';
import { DatabaseModule } from '../database/database.module';

@Module({
  imports: [DatabaseModule],
  providers: [EncryptionService, SessionService, UserService],
  exports: [EncryptionService, SessionService, UserService],
})
export class SecurityModule {}