import { Inject, Injectable, Logger } from '@nestjs/common';
import { and, eq, lt } from 'drizzle-orm';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { Request } from 'express';
import { DATABASE_CONNECTION } from '../database/database.service';
import { Session, sessions } from '../database/entities/session.entity';
import { users } from '../database/entities/user.entity';
import { EncryptionService } from './encryption.service';

@Injectable()
export class SessionService {
  private readonly logger = new Logger(SessionService.name);

  constructor(
    @Inject(DATABASE_CONNECTION)
    private readonly db: NodePgDatabase,
    private readonly encryptionService: EncryptionService,
  ) {}

  async createSession(
    userId: string,
    req: Request,
    expiresIn: number = 24 * 60 * 60 * 1000, // 24 hours
  ): Promise<Session> {
    try {
      const sessionId = this.encryptionService.generateSessionId();
      const expiresAt = new Date(Date.now() + expiresIn);
      const ipAddress = req.ip || req.connection?.remoteAddress || 'unknown';
      const userAgent = req.get('User-Agent') || 'unknown';
      const deviceFingerprint = this.encryptionService.generateDeviceFingerprint(
        userAgent,
        ipAddress,
      );

      const newSession = {
        sessionId,
        userId,
        expiresAt,
        ipAddress,
        userAgent,
        deviceFingerprint,
        lastAccessedAt: new Date(),
        isRevoked: false,
      };

      const result = await this.db.insert(sessions).values(newSession as any).returning();
      return result[0];
    } catch (error) {
      this.logger.error('Failed to create session:', error);
      throw new Error('Session creation failed');
    }
  }

  async validateSession(sessionId: string, req: Request): Promise<(Session & { user?: any }) | null> {
    try {
      const sessionResults = await this.db
        .select()
        .from(sessions)
        .leftJoin(users, eq(sessions.userId, users.discordId))
        .where(and(
          eq(sessions.sessionId, sessionId),
          eq(sessions.isRevoked, false)
        ));

      if (sessionResults.length === 0) {
        return null;
      }

      const session = sessionResults[0].sessions;
      const user = sessionResults[0].users;

      // Check if session is expired
      if (session.expiresAt < new Date()) {
        await this.revokeSession(sessionId);
        return null;
      }

      // Update last accessed time
      await this.db
        .update(sessions)
        .set({ lastAccessedAt: new Date() } as any)
        .where(eq(sessions.sessionId, sessionId));

      return { ...session, lastAccessedAt: new Date(), user };
    } catch (error) {
      this.logger.error('Failed to validate session:', error);
      return null;
    }
  }

  async revokeSession(sessionId: string): Promise<void> {
    try {
      await this.db
        .update(sessions)
        .set({ isRevoked: true } as any)
        .where(eq(sessions.sessionId, sessionId));
    } catch (error) {
      this.logger.error('Failed to revoke session:', error);
    }
  }

  async cleanupExpiredSessions(): Promise<number> {
    try {
      const result = await this.db
        .delete(sessions)
        .where(lt(sessions.expiresAt, new Date()));
      return result.rowCount || 0;
    } catch (error) {
      this.logger.error('Failed to cleanup expired sessions:', error);
      return 0;
    }
  }
}