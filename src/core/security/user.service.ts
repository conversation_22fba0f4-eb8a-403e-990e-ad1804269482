import { Inject, Injectable, Logger } from '@nestjs/common';
import { eq, sql } from 'drizzle-orm';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { DATABASE_CONNECTION } from '../database/database.service';
import { User, users } from '../database/entities/user.entity';
import * as schema from '../database/schema';
import { EncryptionService } from './encryption.service';

@Injectable()
export class UserService {
  private readonly logger = new Logger(UserService.name);

  constructor(
    @Inject(DATABASE_CONNECTION) private db: NodePgDatabase<typeof schema>,
    private readonly encryptionService: EncryptionService,
  ) {}

  async findByDiscordId(discordId: string): Promise<User | null> {
    try {
      const result = await this.db.select().from(users).where(eq(users.discordId, discordId)).limit(1);
      return result[0] || null;
    } catch (error) {
      this.logger.error(`Failed to find user by Discord ID ${discordId}:`, error);
      return null;
    }
  }

  async createUser(userData: {
    discordId: string;
    username: string;
    email?: string;
    accessToken?: string;
    refreshToken?: string;
  }): Promise<User> {
    try {
      const newUser = {
        discordId: userData.discordId,
        username: userData.username,
        email: userData.email,
        accessToken: userData.accessToken ?
          this.encryptionService.encryptUserData(userData.accessToken) : undefined,
        refreshToken: userData.refreshToken ?
          this.encryptionService.encryptUserData(userData.refreshToken) : undefined,
        isActive: true,
        lastActivityAt: new Date(),
      };

      const result = await this.db.insert(users).values(newUser as any).returning();
      return result[0];
    } catch (error) {
      this.logger.error('Failed to create user:', error);
      throw new Error('User creation failed');
    }
  }

  async updateUser(discordId: string, updates: Partial<User>): Promise<User | null> {
    try {
      await this.db.update(users).set(updates).where(eq(users.discordId, discordId));
      return await this.findByDiscordId(discordId);
    } catch (error) {
      this.logger.error(`Failed to update user ${discordId}:`, error);
      return null;
    }
  }

  async upsertUser(userData: {
    discordId: string;
    username: string;
    email?: string;
    accessToken?: string;
    refreshToken?: string;
  }): Promise<User> {
    const existingUser = await this.findByDiscordId(userData.discordId);
    
    if (existingUser) {
      const updated = await this.updateUser(userData.discordId, {
        username: userData.username,
        email: userData.email,
        lastActivityAt: new Date(),
      });
      return updated!;
    } else {
      return await this.createUser(userData);
    }
  }

  async getUserCount(): Promise<number> {
    try {
      const result = await this.db.select({ count: sql`count(*)` }).from(users).where(eq(users.isActive, true));
      return Number(result[0].count);
    } catch (error) {
      this.logger.error('Failed to get user count:', error);
      return 0;
    }
  }
}