import { Inject, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { sql } from 'drizzle-orm';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';
import * as schema from './schema';

export const DATABASE_CONNECTION = Symbol('DATABASE_CONNECTION');

@Injectable()
export class DatabaseService {
  private readonly logger = new Logger(DatabaseService.name);

  constructor(
    @Inject(DATABASE_CONNECTION) private db: NodePgDatabase<typeof schema>,
    private configService: ConfigService,
  ) {}

  getDb() {
    return this.db;
  }

  async query<T = any>(text: string, params?: any[]): Promise<T[]> {
    try {
      const result = await this.db.execute(sql.raw(text));
      return result.rows as T[];
    } catch (error) {
      this.logger.error(`Database query failed: ${text}`, error);
      throw error;
    }
  }

  async healthCheck(): Promise<boolean> {
    try {
      await this.db.execute(sql`SELECT 1`);
      return true;
    } catch (error) {
      this.logger.error('Database health check failed', error);
      return false;
    }
  }

  async createDatabaseConnection() {
    this.logger.log('Database connection ready');
    return this.db;
  }
}