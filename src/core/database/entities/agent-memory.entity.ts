import { relations } from 'drizzle-orm';
import { index, integer, jsonb, pgTable, text, timestamp, varchar } from 'drizzle-orm/pg-core';
import { baseColumns } from './base.entity';
import { users } from './user.entity';

export const agentMemory = pgTable('agent_memory', {
  ...baseColumns,
  userId: text('user_id').notNull(),
  memoryType: varchar('memory_type', { length: 50 }).notNull(),
  key: varchar('key', { length: 200 }).notNull(),
  value: jsonb('value').$type<{
    // For assessment memories
    response?: string;
    score?: number;
    category?: string;

    // For progress tracking
    completed?: number;
    active?: number;
    streak?: number;
    weeklyPercent?: number;
    overall?: string;
    totalCheckins?: number;
    lastCheckin?: string;
    weeklyCheckins?: number;

    // For conversation memories
    context?: string;
    summary?: string;
    sentiment?: 'positive' | 'neutral' | 'negative';

    // For goal memories
    goals?: Array<{
      id: string;
      title: string;
      description: string;
      status: 'active' | 'completed' | 'paused';
      progress: number;
      dueDate?: string;
    }>;

    // For interaction memories
    interactionType?: string;
    outcome?: string;
    feedback?: string;

    // Generic properties
    data?: Record<string, any>;
    metadata?: Record<string, any>;
  }>().notNull(),
  context: text('context'),
  importance: integer('importance').default(1).notNull(),
  accessCount: integer('access_count').default(0).notNull(),
  lastAccessedAt: timestamp('last_accessed_at', { withTimezone: true }),
  expiresAt: timestamp('expires_at', { withTimezone: true }),
  tags: text('tags').array(),
  metadata: jsonb('metadata'),
}, (table) => ({
  userIdIdx: index('agent_memory_user_id_idx').on(table.userId),
  memoryTypeIdx: index('agent_memory_memory_type_idx').on(table.memoryType),
  createdAtIdx: index('agent_memory_created_at_idx').on(table.createdAt),
}));

export const agentMemoryRelations = relations(agentMemory, ({ one }) => ({
  user: one(users, {
    fields: [agentMemory.userId],
    references: [users.discordId],
  }),
}));

export type AgentMemory = typeof agentMemory.$inferSelect;
export type NewAgentMemory = typeof agentMemory.$inferInsert;

export type MemoryType = 'assessment' | 'profile' | 'preference' | 'goal' | 'achievement' | 'conversation' | 'insight' | 'pattern' | 'interaction' | 'metrics' | 'routing';