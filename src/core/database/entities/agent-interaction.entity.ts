import { relations } from 'drizzle-orm';
import { index, integer, jsonb, pgTable, text, varchar } from 'drizzle-orm/pg-core';
import { baseColumns } from './base.entity';
import { users } from './user.entity';

export const agentInteractions = pgTable('agent_interactions', {
  ...baseColumns,
  userId: text('user_id').notNull(),
  agentType: varchar('agent_type', { length: 50 }).notNull(),
  interactionType: varchar('interaction_type', { length: 50 }).notNull(),
  content: text('content').notNull(),
  response: text('response'),
  status: varchar('status', { length: 20 }).default('completed').notNull(),
  context: jsonb('context').$type<{
    topic?: string;
    channel?: string;
    previousMessages?: string[];
    userMood?: 'positive' | 'neutral' | 'negative';
    conversationFlow?: string;
    triggerEvent?: string;
    sessionId?: string;
    relatedInteractions?: string[];
  }>(),
  metadata: jsonb('metadata').$type<{
    processingTime?: number;
    confidence?: number;
    model?: string;
    tokens?: number;
    cost?: number;
    retries?: number;
    errorCount?: number;
    source?: string;
    version?: string;
  }>(),
  channelId: text('channel_id'),
  messageId: text('message_id'),
  guildId: text('guild_id'),
  sentimentScore: integer('sentiment_score').default(0),
  tags: text('tags').array(),
}, (table) => ({
  userIdIdx: index('agent_interactions_user_id_idx').on(table.userId),
  agentTypeIdx: index('agent_interactions_agent_type_idx').on(table.agentType),
  interactionTypeIdx: index('agent_interactions_interaction_type_idx').on(table.interactionType),
  createdAtIdx: index('agent_interactions_created_at_idx').on(table.createdAt),
}));

export const agentInteractionsRelations = relations(agentInteractions, ({ one }) => ({
  user: one(users, {
    fields: [agentInteractions.userId],
    references: [users.discordId],
  }),
}));

export type AgentInteraction = typeof agentInteractions.$inferSelect;
export type NewAgentInteraction = typeof agentInteractions.$inferInsert;

// AgentType is already exported from ai-agent-config.entity.ts
export type InteractionType = 'message' | 'command' | 'check_in' | 'assessment' | 'goal_setting' | 'progress_update';
export type InteractionStatus = 'pending' | 'processing' | 'completed' | 'failed';