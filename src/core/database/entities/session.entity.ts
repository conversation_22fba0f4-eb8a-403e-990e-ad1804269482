import { pgTable, varchar, text, timestamp, boolean, jsonb, index } from 'drizzle-orm/pg-core';
import { baseColumns, BaseEntity } from './base.entity';
import { relations } from 'drizzle-orm';
import { users } from './user.entity';

export const sessions = pgTable('sessions', {
  ...baseColumns,
  sessionId: varchar('session_id', { length: 128 }).notNull().unique(),
  userId: text('user_id').notNull(),
  encryptedData: text('encrypted_data'),
  expiresAt: timestamp('expires_at', { withTimezone: true }).notNull(),
  ipAddress: varchar('ip_address', { length: 45 }),
  userAgent: text('user_agent'),
  deviceFingerprint: text('device_fingerprint'),
  isRevoked: boolean('is_revoked').default(false).notNull(),
  lastAccessedAt: timestamp('last_accessed_at', { withTimezone: true }),
  metadata: jsonb('metadata'),
}, (table) => ({
  sessionIdIdx: index('sessions_session_id_idx').on(table.sessionId),
  userIdIdx: index('sessions_user_id_idx').on(table.userId),
  expiresAtIdx: index('sessions_expires_at_idx').on(table.expiresAt),
}));

export const sessionsRelations = relations(sessions, ({ one }) => ({
  user: one(users, {
    fields: [sessions.userId],
    references: [users.discordId],
  }),
}));

export type Session = typeof sessions.$inferSelect;
export type NewSession = typeof sessions.$inferInsert;