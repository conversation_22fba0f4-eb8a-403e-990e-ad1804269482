import { relations } from 'drizzle-orm';
import { boolean, index, integer, jsonb, pgTable, text, timestamp, varchar } from 'drizzle-orm/pg-core';
import type { GuildFeatures, GuildSettings, WelcomeRoles } from '../types';
import { baseColumns } from './base.entity';

export const guilds = pgTable('guilds', {
  ...baseColumns,
  discordId: varchar('discord_id', { length: 50 }).notNull().unique(),
  name: varchar('name', { length: 100 }).notNull(),
  icon: text('icon'),
  isActive: boolean('is_active').default(true).notNull(),
  settings: jsonb('settings').$type<GuildSettings>(),
  features: jsonb('features').$type<GuildFeatures>(),
  lastActivityAt: timestamp('last_activity_at', { withTimezone: true }),
  ownerDiscordId: varchar('owner_discord_id', { length: 50 }),
  welcomeEnabled: boolean('welcome_enabled').default(false).notNull(),
  welcomeChannelId: varchar('welcome_channel_id', { length: 50 }),
  welcomeMessage: text('welcome_message'),
  welcomeRoles: jsonb('welcome_roles').$type<WelcomeRoles>(),
  starboardEnabled: boolean('starboard_enabled').default(false).notNull(),
  starboardChannelId: varchar('starboard_channel_id', { length: 50 }),
  starboardThreshold: integer('starboard_threshold').default(3).notNull(),
  guildId: varchar('guild_id', { length: 50 }),
}, (table) => ({
  discordIdIdx: index('guilds_discord_id_idx').on(table.discordId),
}));

export const guildsRelations = relations(guilds, ({ many }) => ({
  aiAgentConfigs: many(aiAgentConfigs),
}));

export type Guild = typeof guilds.$inferSelect;
export type NewGuild = typeof guilds.$inferInsert;

// Import for relations
import { aiAgentConfigs } from './ai-agent-config.entity';
