import { relations } from 'drizzle-orm';
import { boolean, index, integer, jsonb, pgTable, text, timestamp, varchar } from 'drizzle-orm/pg-core';
import { baseColumns } from './base.entity';

export const users = pgTable('users', {
  ...baseColumns,
  discordId: varchar('discord_id', { length: 50 }).notNull().unique(),
  username: varchar('username', { length: 100 }).notNull(),
  email: varchar('email', { length: 255 }),
  isActive: boolean('is_active').default(true).notNull(),
  lastActivityAt: timestamp('last_activity_at', { withTimezone: true }),
  preferences: jsonb('preferences').$type<{
    notifications?: {
      enabled: boolean;
      types: string[];
      frequency: 'immediate' | 'daily' | 'weekly';
    };
    privacy?: {
      shareProgress: boolean;
      allowDataCollection: boolean;
      showOnLeaderboard: boolean;
    };
    ai?: {
      preferredAgent: string;
      responseStyle: 'formal' | 'casual' | 'friendly';
      maxResponseLength: number;
    };
    timezone?: string;
    language?: string;
    dailyStreak?: number;
    lastDaily?: string;
    devRequests?: Array<{
      id: string;
      clientId: string;
      clientTag: string;
      description: string;
      budget?: string;
      timeline?: string;
      skills: string[];
      status: 'open' | 'assigned' | 'in_progress' | 'completed' | 'cancelled';
      developerId?: string;
      developerTag?: string;
      createdAt: string;
      assignedAt?: string;
      channelId?: string;
    }>;
    warnings?: Array<{
      id: string;
      guildId: string;
      reason: string;
      moderatorId: string;
      timestamp: string;
      severity: 'low' | 'medium' | 'high';
    }>;
  }>(),
  profile: jsonb('profile').$type<{
    displayName?: string;
    bio?: string;
    avatar?: string;
    badges?: string[];
    achievements?: Array<{
      id: string;
      name: string;
      description: string;
      unlockedAt: string;
      rarity: 'common' | 'rare' | 'epic' | 'legendary';
    }>;
    stats?: {
      messagesCount: number;
      commandsUsed: number;
      timeSpent: number;
      level: number;
      experience: number;
    };
    goals?: Array<{
      id: string;
      title: string;
      category: string;
      status: 'active' | 'completed' | 'paused';
      progress: number;
      createdAt: string;
      targetDate?: string;
    }>;
  }>(),
  accessToken: text('access_token'),
  refreshToken: text('refresh_token'),
  tokenExpiresAt: timestamp('token_expires_at', { withTimezone: true }),
  experience: integer('experience').default(0).notNull(),
  balance: integer('balance').default(0).notNull(),
  userId: varchar('user_id', { length: 50 }),
  discriminator: varchar('discriminator', { length: 10 }),
  avatarUrl: text('avatar_url'),
}, (table) => ({
  discordIdIdx: index('users_discord_id_idx').on(table.discordId),
  createdAtIdx: index('users_created_at_idx').on(table.createdAt),
  usernameIdx: index('users_username_idx').on(table.username),
}));

export const usersRelations = relations(users, ({ many }) => ({
  sessions: many(sessions),
  relationships: many(userRelationships),
  agentInteractions: many(agentInteractions),
  growthPlans: many(personalGrowthPlans),
}));

export type User = typeof users.$inferSelect;
export type NewUser = typeof users.$inferInsert;

// Import statements for relations
import { agentInteractions } from './agent-interaction.entity';
import { personalGrowthPlans } from './personal-growth-plan.entity';
import { sessions } from './session.entity';
import { userRelationships } from './user-relationship.entity';

