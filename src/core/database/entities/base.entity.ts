import { serial, timestamp } from 'drizzle-orm/pg-core';

export const baseColumns = {
  id: serial('id').primaryKey(),
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
  deletedAt: timestamp('deleted_at', { withTimezone: true }),
};

export type BaseEntity = {
  id: number;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date | null;
};