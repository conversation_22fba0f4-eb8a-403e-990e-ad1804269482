import { pgTable, text, varchar, jsonb, timestamp, integer, index } from 'drizzle-orm/pg-core';
import { baseColumns, BaseEntity } from './base.entity';
import { relations } from 'drizzle-orm';
import { users } from './user.entity';

export const personalGrowthPlans = pgTable('personal_growth_plans', {
  ...baseColumns,
  userId: text('user_id').notNull(),
  title: varchar('title', { length: 200 }).notNull(),
  description: text('description'),
  status: varchar('status', { length: 20 }).default('active').notNull(),
  goals: jsonb('goals'),
  milestones: jsonb('milestones'),
  startDate: timestamp('start_date', { withTimezone: true }),
  targetCompletionDate: timestamp('target_completion_date', { withTimezone: true }),
  actualCompletionDate: timestamp('actual_completion_date', { withTimezone: true }),
  progressPercentage: integer('progress_percentage').default(0).notNull(),
  metadata: jsonb('metadata'),
}, (table) => ({
  userIdIdx: index('personal_growth_plans_user_id_idx').on(table.userId),
  statusIdx: index('personal_growth_plans_status_idx').on(table.status),
}));

export const personalGrowthPlansRelations = relations(personalGrowthPlans, ({ one }) => ({
  user: one(users, {
    fields: [personalGrowthPlans.userId],
    references: [users.discordId],
  }),
}));

export type PersonalGrowthPlan = typeof personalGrowthPlans.$inferSelect;
export type NewPersonalGrowthPlan = typeof personalGrowthPlans.$inferInsert;

export type PlanStatus = 'draft' | 'active' | 'completed' | 'paused' | 'cancelled';
export type Goal = {
  id: string;
  title: string;
  description?: string;
  status: 'pending' | 'in_progress' | 'completed';
  targetDate?: string;
  progress?: number;
};
export type Milestone = {
  id: string;
  title: string;
  description?: string;
  completed: boolean;
  completedAt?: string;
};