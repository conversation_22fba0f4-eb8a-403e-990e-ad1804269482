import { relations } from 'drizzle-orm';
import { boolean, index, jsonb, pgTable, text, timestamp, varchar } from 'drizzle-orm/pg-core';
import { baseColumns } from './base.entity';
import { guilds } from './guild.entity';

export const aiAgentConfigs = pgTable('ai_agent_config', {
  ...baseColumns,
  guildId: text('guild_id').notNull(),
  agentType: varchar('agent_type', { length: 50 }).notNull(),
  enabled: boolean('enabled').default(true).notNull(),
  configuration: jsonb('configuration').$type<{
    channels?: string[];
    defaultAgent?: string;
    schedules?: Array<{
      type: 'daily' | 'weekly' | 'monthly';
      time: string;
      timezone?: string;
      enabled: boolean;
    }>;
    prompts?: Record<string, string>;
    personality?: {
      tone: string;
      style: string;
      expertise: string[];
      responseLength: 'short' | 'medium' | 'long';
    };
    triggers?: {
      keywords?: string[];
      reactions?: string[];
      conditions?: Record<string, any>;
      autoRespond?: boolean;
    };
    limits?: {
      maxResponsesPerHour: number;
      maxTokensPerResponse: number;
      cooldownSeconds: number;
    };
  }>(),
  defaultChannelId: text('default_channel_id'),
  logChannelId: text('log_channel_id'),
  permissions: jsonb('permissions'),
  settings: jsonb('settings'),
  lastUsedAt: timestamp('last_used_at', { withTimezone: true }),
}, (table) => ({
  guildIdIdx: index('ai_agent_config_guild_id_idx').on(table.guildId),
  agentTypeIdx: index('ai_agent_config_agent_type_idx').on(table.agentType),
}));

export const aiAgentConfigsRelations = relations(aiAgentConfigs, ({ one }) => ({
  guild: one(guilds, {
    fields: [aiAgentConfigs.guildId],
    references: [guilds.discordId],
  }),
}));

export type AIAgentConfig = typeof aiAgentConfigs.$inferSelect;
export type NewAIAgentConfig = typeof aiAgentConfigs.$inferInsert;

export type AgentType = 'personal_growth_coach' | 'intake_specialist' | 'progress_tracker' | 'general';
export type AgentConfiguration = {
  channels?: string[];
  schedules?: Array<{
    type: 'daily' | 'weekly' | 'monthly';
    time: string;
    timezone?: string;
  }>;
  prompts?: Record<string, string>;
  personality?: {
    tone: string;
    style: string;
    expertise: string[];
  };
  triggers?: {
    keywords?: string[];
    reactions?: string[];
    conditions?: Record<string, any>;
  };
};
export type AgentPermissions = {
  allowedRoles?: string[];
  restrictedRoles?: string[];
  allowedUsers?: string[];
  restrictedUsers?: string[];
};