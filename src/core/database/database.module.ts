import { Global, Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { drizzle } from 'drizzle-orm/node-postgres';
import { migrate } from 'drizzle-orm/node-postgres/migrator';
import { Pool } from 'pg';
import { DATABASE_CONNECTION, DatabaseService } from './database.service';
import * as schema from './schema';

@Global()
@Module({
  providers: [
    {
      provide: DATABASE_CONNECTION,
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => {
        const connectionString = configService.get<string>('DATABASE_URL');
        
        const pool = new Pool({
          connectionString,
          ssl: configService.get('NODE_ENV') === 'production' 
            ? { rejectUnauthorized: false } 
            : false,
        });
        
        const db = drizzle(pool, { schema, logger: configService.get('NODE_ENV') !== 'production' });
        
        // Run migrations only when explicitly requested
        // For development, use db:migrate command instead
        // For production, ensure migrations are run during deployment
        const shouldRunMigrations = configService.get('RUN_MIGRATIONS') === 'true';
        if (shouldRunMigrations) {
          try {
            await migrate(db, { migrationsFolder: './migrations' });
          } catch (error) {
            // Log migration error but don't fail startup if tables already exist
            if (error.message?.includes('already exists')) {
              console.warn('⚠️ Migration warning: Tables already exist, skipping migration');
            } else {
              console.error('❌ Migration failed:', error);
              throw error;
            }
          }
        }
        
        return db;
      },
    },
    DatabaseService,
  ],
  exports: [DatabaseService, DATABASE_CONNECTION],
})
export class DatabaseModule {}