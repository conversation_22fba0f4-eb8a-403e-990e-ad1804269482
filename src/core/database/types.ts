// Database JSONB field type definitions
// This file provides TypeScript interfaces for JSONB columns to ensure type safety

// Guild Settings Types
export interface GuildSettings {
  roleAccess?: {
    enabled: boolean;
    tiers: Array<{
      id: string;
      name: string;
      roleIds: string[];
      permissions: string[];
      priority: number;
    }>;
    defaultTier?: string;
    autoAssign?: boolean;
    logChannel?: string;
    restrictedChannels?: string[];
  };
  moderation?: {
    enabled: boolean;
    autoMod: boolean;
    logChannelId?: string;
    logChannel?: string;
    mutedRoleId?: string;
    warningThreshold: number;
  };
  economy?: {
    enabled: boolean;
    currency: string;
    dailyReward: number;
    workCooldown: number;
  };
  leveling?: {
    enabled: boolean;
    xpPerMessage: number;
    levelUpChannelId?: string;
    roleRewards: Array<{
      level: number;
      roleId: string;
    }>;
  };
  devOnDemand?: {
    enabled: boolean;
    requestChannel?: string;
    notificationChannel?: string;
    developerRole?: string;
    clientRole?: string;
    maxActiveRequests?: number;
    requestTimeoutHours?: number;
    autoAssignment?: boolean;
    autoAssignDevelopers?: boolean;
    requireApproval?: boolean;
    allowedChannels?: string[];
  };
  aiAutomation?: {
    enabled: boolean;
    weekly_insights?: boolean;
    engagement_digest?: boolean;
    member_insights?: boolean;
    auto_moderation?: boolean;
    auto_engagement?: boolean;
    content_suggestions?: boolean;
    smart_greetings?: boolean;
    smart_notifications?: boolean;
    auto_responses?: boolean;
    sentiment_analysis?: boolean;
    topic_tracking?: boolean;
    engagement_optimization?: boolean;
  };
  reactionRole?: {
    enabled: boolean;
    roles: Array<{
      messageId: string;
      channelId: string;
      emoji: string;
      roleId: string;
    }>;
  };
  whop?: {
    enabled: boolean;
    apiKey?: string;
    webhookUrl?: string;
    productIds?: string[];
  };
  meme?: {
    enabled: boolean;
    allowedChannels?: string[];
    cooldownSeconds?: number;
  };
  userCommand?: {
    enabled: boolean;
    prefix?: string;
    allowedChannels?: string[];
  };
  gaming?: {
    enabled: boolean;
    allowedGames?: string[];
    tournamentChannelId?: string;
    leaderboardChannelId?: string;
  };
  utility?: {
    enabled: boolean;
    allowedChannels?: string[];
    features?: string[];
  };
  music?: {
    enabled: boolean;
    maxQueueSize?: number;
    allowPlaylists?: boolean;
    djRoleId?: string;
    defaultVolume?: number;
  };
}

// Guild Features Types
export interface GuildFeatures {
  music?: {
    enabled: boolean;
    maxQueueSize: number;
    allowPlaylists: boolean;
    djRoleId?: string;
  };
  gaming?: {
    enabled: boolean;
    allowedGames: string[];
    tournamentChannelId?: string;
  };
  ai?: {
    enabled: boolean;
    defaultModel: string;
    maxTokens: number;
    allowedChannels: string[];
  };
}

// AI Agent Configuration Types
export interface AgentConfiguration {
  channels?: string[];
  defaultAgent?: string;
  schedules?: Array<{
    type: 'daily' | 'weekly' | 'monthly';
    time: string;
    timezone?: string;
    enabled: boolean;
  }>;
  prompts?: Record<string, string>;
  personality?: {
    tone: string;
    style: string;
    expertise: string[];
    responseLength: 'short' | 'medium' | 'long';
  };
  triggers?: {
    keywords?: string[];
    reactions?: string[];
    conditions?: Record<string, any>;
    autoRespond?: boolean;
  };
  limits?: {
    maxResponsesPerHour: number;
    maxTokensPerResponse: number;
    cooldownSeconds: number;
  };
}

// Agent Permissions Types
export interface AgentPermissions {
  allowedRoles?: string[];
  restrictedRoles?: string[];
  allowedUsers?: string[];
  restrictedUsers?: string[];
  allowedChannels?: string[];
  restrictedChannels?: string[];
  requirePermission?: boolean;
}

// Agent Settings Types
export interface AgentSettings {
  enabled: boolean;
  debug: boolean;
  logLevel: 'error' | 'warn' | 'info' | 'debug';
  autoLearn: boolean;
  contextWindow: number;
  temperature: number;
  maxRetries: number;
}

// Agent Memory Value Types
export interface MemoryValue {
  // For assessment memories
  response?: string;
  score?: number;
  category?: string;
  
  // For progress tracking
  completed?: number;
  active?: number;
  streak?: number;
  weeklyPercent?: number;
  overall?: string;
  
  // For conversation memories
  context?: string;
  summary?: string;
  sentiment?: 'positive' | 'neutral' | 'negative';
  
  // For goal memories
  goals?: Array<{
    id: string;
    title: string;
    description: string;
    status: 'active' | 'completed' | 'paused';
    progress: number;
    dueDate?: string;
  }>;
  
  // For interaction memories
  interactionType?: string;
  outcome?: string;
  feedback?: string;
  
  // Generic properties
  data?: Record<string, any>;
  metadata?: Record<string, any>;
}

// User Preferences Types
export interface UserPreferences {
  notifications?: {
    enabled: boolean;
    types: string[];
    frequency: 'immediate' | 'daily' | 'weekly';
  };
  privacy?: {
    shareProgress: boolean;
    allowDataCollection: boolean;
    showOnLeaderboard: boolean;
  };
  ai?: {
    preferredAgent: string;
    responseStyle: 'formal' | 'casual' | 'friendly';
    maxResponseLength: number;
  };
  timezone?: string;
  language?: string;
  warnings?: Array<{
    id: string;
    guildId: string;
    reason: string;
    moderatorId: string;
    timestamp: string;
    severity: 'low' | 'medium' | 'high';
  }>;
}

// User Profile Types
export interface UserProfile {
  displayName?: string;
  bio?: string;
  avatar?: string;
  badges?: string[];
  achievements?: Array<{
    id: string;
    name: string;
    description: string;
    unlockedAt: string;
    rarity: 'common' | 'rare' | 'epic' | 'legendary';
  }>;
  stats?: {
    messagesCount: number;
    commandsUsed: number;
    timeSpent: number;
    level: number;
    experience: number;
  };
  goals?: Array<{
    id: string;
    title: string;
    category: string;
    status: 'active' | 'completed' | 'paused';
    progress: number;
    createdAt: string;
    targetDate?: string;
  }>;
}

// Welcome Roles Types
export interface WelcomeRoles {
  autoAssign?: string[];
  selectable?: Array<{
    roleId: string;
    name: string;
    description: string;
    emoji?: string;
    category?: string;
  }>;
}

// Session Metadata Types
export interface SessionMetadata {
  userAgent?: string;
  ipAddress?: string;
  deviceFingerprint?: string;
  loginMethod?: 'discord' | 'oauth' | 'token';
  lastActivity?: string;
  permissions?: string[];
}
