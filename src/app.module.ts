import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ScheduleModule } from '@nestjs/schedule';
import { NecordModule } from 'necord';
import { GatewayIntentBits } from 'discord.js';

// Core modules
import { CoreModule } from './core/core.module';
import { DatabaseModule } from './core/database/database.module';
import { SecurityModule } from './core/security/security.module';

// Feature modules
import { DiscordModule } from './discord/discord.module';
import { ApiModule } from './api/api.module';
import { AgentsModule } from './agents/agents.module';
import { FeaturesModule } from './features/features.module';

// Configuration
import { configValidation } from './core/config/config.validation';

@Module({
  imports: [
    // Configuration
    ConfigModule.forRoot({
      isGlobal: true,
      validate: configValidation,
      envFilePath: ['.env.local', '.env'],
    }),

    // Schedule for cron jobs
    ScheduleModule.forRoot(),

    // Discord client
    NecordModule.forRootAsync({
      useFactory: () => ({
        token: process.env.DISCORD_TOKEN!,
        intents: [
          GatewayIntentBits.Guilds,
          GatewayIntentBits.GuildMembers,
          GatewayIntentBits.GuildMessages,
          GatewayIntentBits.GuildPresences,
          GatewayIntentBits.DirectMessages,
          GatewayIntentBits.MessageContent,
        ],
        development: process.env.NODE_ENV === 'development' && process.env.GUILD_ID ? [process.env.GUILD_ID] : false,
      }),
    }),

    // Core infrastructure
    CoreModule,
    DatabaseModule,
    SecurityModule,

    // Feature modules
    DiscordModule,
    ApiModule,
    AgentsModule,
    FeaturesModule,
  ],
})
export class AppModule {}