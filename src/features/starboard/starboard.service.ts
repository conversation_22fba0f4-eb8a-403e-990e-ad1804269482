import { Injectable, Logger, Inject } from '@nestjs/common';
import { On, Context } from 'necord';
import { MessageReaction, User, TextChannel, EmbedBuilder } from 'discord.js';
import { DATABASE_CONNECTION } from '../../core/database/database.service';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { eq } from 'drizzle-orm';
import { guilds, Guild, NewGuild } from '../../core/database/schema';

@Injectable()
export class StarboardService {
  private readonly logger = new Logger(StarboardService.name);
  private readonly processedMessages = new Set<string>();

  constructor(
    @Inject(DATABASE_CONNECTION) private readonly db: NodePgDatabase,
  ) {}

  @On('messageReactionAdd')
  async handleReactionAdd(@Context() [reaction, user]: [MessageReaction, User]) {
    if (user.bot) return;
    if (reaction.emoji.name !== '⭐') return;

    try {
      const guildResults = await this.db.select().from(guilds).where(eq(guilds.discordId, reaction.message.guild?.id));
      const guild = guildResults[0];

      if (!guild || !guild.starboardEnabled || !guild.starboardChannelId) {
        return;
      }

      const messageId = reaction.message.id;
      const starCount = reaction.count || 0;
      const requiredStars = guild.starboardThreshold || 3;

      if (starCount >= requiredStars && !this.processedMessages.has(messageId)) {
        await this.addToStarboard(reaction, guild);
        this.processedMessages.add(messageId);
      }
    } catch (error) {
      this.logger.error('Failed to handle star reaction:', error);
    }
  }

  private async addToStarboard(reaction: MessageReaction, guild: Guild) {
    try {
      const starboardChannel = reaction.message.guild?.channels.cache.get(
        guild.starboardChannelId!
      ) as TextChannel;

      if (!starboardChannel) return;

      const message = reaction.message;
      const author = message.author;

      if (!author) return;

      const embed = new EmbedBuilder()
        .setAuthor({
          name: author.tag,
          iconURL: author.displayAvatarURL(),
        })
        .setDescription(message.content || '*[No text content]*')
        .setColor(0xFFD700)
        .setTimestamp(message.createdAt)
        .addFields([
          {
            name: 'Original Message',
            value: `[Jump to message](${message.url})`,
            inline: true,
          },
          {
            name: 'Channel',
            value: `${message.channel}`,
            inline: true,
          },
        ]);

      // Add image if present
      const attachment = message.attachments.first();
      if (attachment && attachment.contentType?.startsWith('image/')) {
        embed.setImage(attachment.url);
      }

      await starboardChannel.send({
        content: `⭐ **${reaction.count}** | ${message.channel}`,
        embeds: [embed],
      });

      this.logger.log(`Added message ${message.id} to starboard with ${reaction.count} stars`);
    } catch (error) {
      this.logger.error('Failed to add message to starboard:', error);
    }
  }

  async updateStarboardConfig(
    guildId: string,
    config: {
      enabled?: boolean;
      channelId?: string;
      threshold?: number;
    }
  ) {
    try {
      const guildResults = await this.db.select().from(guilds).where(eq(guilds.discordId, guildId));
      const guild = guildResults[0];

      if (!guild) {
        throw new Error('Guild not found');
      }

      const updateData: Partial<Guild> = {};
      if (config.enabled !== undefined) updateData.starboardEnabled = config.enabled;
      if (config.channelId) updateData.starboardChannelId = config.channelId;
      if (config.threshold !== undefined) updateData.starboardThreshold = config.threshold;

      await this.db.update(guilds).set(updateData).where(eq(guilds.id, guild.id));
      
      // Get updated guild for return value
      const updatedGuildResults = await this.db.select().from(guilds).where(eq(guilds.id, guild.id));
      const updatedGuild = updatedGuildResults[0];
      
      return {
        message: 'Starboard configuration updated successfully',
        config: {
          enabled: updatedGuild.starboardEnabled,
          channelId: updatedGuild.starboardChannelId,
          threshold: updatedGuild.starboardThreshold,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to update starboard config for guild ${guildId}:`, error);
      throw error;
    }
  }

  async getStarboardConfig(guildId: string) {
    try {
      const guildResults = await this.db.select().from(guilds).where(eq(guilds.discordId, guildId));
      const guild = guildResults[0];

      if (!guild) {
        return {
          enabled: true, // Enable by default
          channelId: null,
          threshold: 3,
        };
      }

      return {
        enabled: guild.starboardEnabled !== false, // Default to true unless explicitly disabled
        channelId: guild.starboardChannelId,
        threshold: guild.starboardThreshold || 3,
      };
    } catch (error) {
      this.logger.error(`Failed to get starboard config for guild ${guildId}:`, error);
      throw error;
    }
  }
}