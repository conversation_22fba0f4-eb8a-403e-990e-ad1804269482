import { Inject, Injectable, Logger } from '@nestjs/common';
import { EmbedBuilder, GuildMember, TextChannel } from 'discord.js';
import { eq } from 'drizzle-orm';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { Context, On } from 'necord';
import { DATABASE_CONNECTION } from '../../core/database/database.service';
import { Guild, guilds, NewUser, User, users } from '../../core/database/schema';

@Injectable()
export class WelcomeService {
  private readonly logger = new Logger(WelcomeService.name);

  constructor(
    @Inject(DATABASE_CONNECTION) private readonly db: NodePgDatabase,
  ) {}

  @On('guildMemberAdd')
  async handleMemberJoin(@Context() [member]: [GuildMember]) {
    try {
      // Get guild configuration
      const guildResults = await this.db.select().from(guilds).where(eq(guilds.discordId, member.guild.id));
      const guild = guildResults[0];

      if (!guild || !guild.welcomeEnabled) {
        return;
      }

      // Create or update user record
      await this.createOrUpdateUser(member);

      // Send welcome message
      await this.sendWelcomeMessage(member, guild);

      // Assign welcome roles if configured
      await this.assignWelcomeRoles(member, guild);

      this.logger.log(`Welcome process completed for ${member.user.tag} in ${member.guild.name}`);
    } catch (error) {
      this.logger.error(`Failed to handle member join for ${member.user.tag}:`, error);
    }
  }

  private async createOrUpdateUser(member: GuildMember) {
    try {
      const userResults = await this.db.select().from(users).where(eq(users.discordId, member.user.id));
      let user = userResults[0];

      if (!user) {
        await this.db.insert(users).values({
          discordId: member.user.id,
          username: member.user.username,
          discriminator: member.user.discriminator || '0000',
          avatarUrl: member.user.displayAvatarURL(),
          isActive: true,
        } as NewUser);
      } else {
        await this.db.update(users).set({
          username: member.user.username,
          discriminator: member.user.discriminator || '0000',
          avatarUrl: member.user.displayAvatarURL(),
          isActive: true,
        } as Partial<User>).where(eq(users.id, user.id));
      }
    } catch (error) {
      this.logger.error(`Failed to create/update user ${member.user.tag}:`, error);
    }
  }

  private async sendWelcomeMessage(member: GuildMember, guild: Guild) {
    try {
      const welcomeChannelId = guild.welcomeChannelId;
      if (!welcomeChannelId) return;

      const channel = member.guild.channels.cache.get(welcomeChannelId) as TextChannel;
      if (!channel) return;

      const embed = new EmbedBuilder()
        .setTitle(`Welcome to ${member.guild.name}! 🎉`)
        .setDescription(this.getWelcomeMessage(member, guild))
        .setColor(0x00AE86)
        .setThumbnail(member.user.displayAvatarURL())
        .setTimestamp()
        .setFooter({
          text: `Member #${member.guild.memberCount}`,
          iconURL: member.guild.iconURL() || undefined,
        });

      await channel.send({ embeds: [embed] });
    } catch (error) {
      this.logger.error(`Failed to send welcome message for ${member.user.tag}:`, error);
    }
  }

  private getWelcomeMessage(member: GuildMember, guild: Guild): string {
    const customMessage = guild.welcomeMessage;
    
    if (customMessage) {
      return customMessage
        .replace('{user}', `<@${member.user.id}>`)
        .replace('{guild}', member.guild.name)
        .replace('{memberCount}', member.guild.memberCount.toString());
    }

    return `Hey <@${member.user.id}>! Welcome to **${member.guild.name}**! 🌟

We're excited to have you join our community of growth-focused individuals. Here's how to get started:

🎯 **Get Started:**
• Use \`/intake\` to complete your personalized assessment
• Use \`/coach\` to connect with your personal growth coach
• Check out our community channels and introduce yourself

💬 **Need Help?**
Feel free to ask questions in our community channels or reach out to our moderators.

Let's grow together! 🚀`;
  }

  private async assignWelcomeRoles(member: GuildMember, guild: Guild) {
    try {
      const welcomeRoles = guild.welcomeRoles as string[] | null;
      if (!welcomeRoles || welcomeRoles.length === 0) return;

      const rolesToAdd = [];
      
      for (const roleId of welcomeRoles) {
        const role = member.guild.roles.cache.get(roleId);
        if (role && !member.roles.cache.has(roleId)) {
          rolesToAdd.push(role);
        }
      }

      if (rolesToAdd.length > 0) {
        await member.roles.add(rolesToAdd);
        this.logger.log(`Assigned welcome roles to ${member.user.tag}: ${rolesToAdd.map(r => r.name).join(', ')}`);
      }
    } catch (error) {
      this.logger.error(`Failed to assign welcome roles to ${member.user.tag}:`, error);
    }
  }

  async updateWelcomeConfig(
    guildId: string,
    config: {
      enabled?: boolean;
      channelId?: string;
      message?: string;
      roles?: string[];
    }
  ) {
    try {
      const guildResults = await this.db.select().from(guilds).where(eq(guilds.discordId, guildId));
      const guild = guildResults[0];

      if (!guild) {
        throw new Error('Guild not found');
      }

      const updateData: Partial<Guild> = {};
      if (config.enabled !== undefined) updateData.welcomeEnabled = config.enabled;
      if (config.channelId) updateData.welcomeChannelId = config.channelId;
      if (config.message) updateData.welcomeMessage = config.message;
      if (config.roles) updateData.welcomeRoles = { autoAssign: config.roles };

      await this.db.update(guilds).set(updateData).where(eq(guilds.id, guild.id));
      
      // Get updated guild for return value
      const updatedGuildResults = await this.db.select().from(guilds).where(eq(guilds.id, guild.id));
      const updatedGuild = updatedGuildResults[0];
      
      return {
        message: 'Welcome configuration updated successfully',
        config: {
          enabled: updatedGuild.welcomeEnabled,
          channelId: updatedGuild.welcomeChannelId,
          message: updatedGuild.welcomeMessage,
          roles: updatedGuild.welcomeRoles,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to update welcome config for guild ${guildId}:`, error);
      throw error;
    }
  }

  async getWelcomeConfig(guildId: string) {
    try {
      const guildResults = await this.db.select().from(guilds).where(eq(guilds.discordId, guildId));
      const guild = guildResults[0];

      if (!guild) {
        return {
          enabled: true, // Enable by default
          channelId: null,
          message: null,
          roles: [],
        };
      }

      return {
        enabled: guild.welcomeEnabled !== false, // Default to true unless explicitly disabled
        channelId: guild.welcomeChannelId,
        message: guild.welcomeMessage,
        roles: guild.welcomeRoles || [],
      };
    } catch (error) {
      this.logger.error(`Failed to get welcome config for guild ${guildId}:`, error);
      throw error;
    }
  }
}