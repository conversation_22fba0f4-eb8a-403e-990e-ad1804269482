import { Inject, Injectable, Logger } from '@nestjs/common';
// @ts-nocheck
import { EmbedBuilder, PermissionFlagsBits, TextChannel } from 'discord.js';
import { eq } from 'drizzle-orm';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { Context, Options, SlashCommand, SlashCommandContext, StringOption } from 'necord';
import { DATABASE_CONNECTION } from '../../core/database/database.service';
import { guilds, User, users } from '../../core/database/schema';

export class DevRequestDto {
  @StringOption({
    name: 'description',
    description: 'Project description',
    required: true,
  })
  description: string;

  @StringOption({
    name: 'budget',
    description: 'Project budget',
    required: false,
  })
  budget?: string;

  @StringOption({
    name: 'timeline',
    description: 'Project timeline',
    required: false,
  })
  timeline?: string;

  @StringOption({
    name: 'skills',
    description: 'Required skills',
    required: false,
  })
  skills?: string;
}

export class DevAcceptDto {
  @StringOption({
    name: 'request_id',
    description: 'The request ID to accept',
    required: true,
  })
  requestId: string;
}

interface DevRequest {
  id: string;
  clientId: string;
  clientTag: string;
  description: string;
  budget?: string;
  timeline?: string;
  skills: string[];
  status: 'open' | 'assigned' | 'in_progress' | 'completed' | 'cancelled';
  developerId?: string;
  developerTag?: string;
  createdAt: string;
  assignedAt?: string;
  channelId?: string;
}

@Injectable()
export class DevOnDemandService {
  private readonly logger = new Logger(DevOnDemandService.name);

  constructor(
    @Inject(DATABASE_CONNECTION) private readonly db: NodePgDatabase,
  ) {}

  @SlashCommand({
    name: 'dev-setup',
    description: 'Set up the developer-on-demand system',
    defaultMemberPermissions: [PermissionFlagsBits.ManageChannels],
  })
  async onDevSetupCommand(@Context() [interaction]: SlashCommandContext) {
    if (!interaction.guild) {
      await interaction.reply({
        content: '❌ This command can only be used in a server.',
        ephemeral: true,
      });
      return;
    }

    try {
      const guildResults = await this.db.select().from(guilds).where(eq(guilds.discordId, interaction.guild.id));
      let guild = guildResults[0];

      if (!guild) {
        const newGuildResults = await this.db.insert(guilds).values({
          discordId: interaction.guild.id,
          name: interaction.guild.name,
          settings: { devOnDemand: { enabled: false } },
        } as any).returning();
        guild = newGuildResults[0];
      }

      const embed = new EmbedBuilder()
        .setColor(0x3B82F6)
        .setTitle('👨‍💻 Developer-on-Demand Setup')
        .setDescription('Connect clients with skilled developers for projects')
        .addFields([
          {
            name: '🚀 Client Commands',
            value: [
              '`/dev-request <description>` - Request a developer',
              '`/dev-requests` - View your active requests',
              '`/dev-cancel <request-id>` - Cancel a request',
            ].join('\n'),
          },
          {
            name: '⚡ Developer Commands',
            value: [
              '`/dev-profile setup` - Set up developer profile',
              '`/dev-browse` - Browse available requests',
              '`/dev-accept <request-id>` - Accept a request',
              '`/dev-complete <request-id>` - Mark request as complete',
            ].join('\n'),
          },
          {
            name: '⚙️ Admin Commands',
            value: [
              '`/dev-config` - Configure system settings',
              '`/dev-stats` - View system statistics',
            ].join('\n'),
          },
        ])
        .setFooter({ text: 'Use the commands above to start connecting clients with developers' });

      await interaction.reply({ embeds: [embed], ephemeral: true });

      // Initialize settings
      if (!guild.settings?.devOnDemand) {
        guild.settings = {
          ...guild.settings,
          devOnDemand: {
            enabled: false,
            requestChannel: null,
            notificationChannel: null,
            developerRole: null,
            clientRole: null,
            maxActiveRequests: 3,
            requestTimeoutHours: 24,
            autoAssignment: false,
          },
        };
        await this.db.update(guilds).set({ settings: guild.settings } as any).where(eq(guilds.id, guild.id));
      }
    } catch (error) {
      this.logger.error('Failed to set up dev-on-demand:', error);
      await interaction.reply({
        content: '❌ Failed to set up developer system. Please try again.',
        ephemeral: true,
      });
    }
  }

  @SlashCommand({
    name: 'dev-request',
    description: 'Request a developer for your project',
  })
  async onDevRequestCommand(
    @Context() [interaction]: SlashCommandContext,
    @Options() { description, budget, timeline, skills }: DevRequestDto
  ) {
    if (!interaction.guild) {
      await interaction.reply({
        content: '❌ This command can only be used in a server.',
        ephemeral: true,
      });
      return;
    }

    try {
      const guildResults = await this.db.select().from(guilds).where(eq(guilds.discordId, interaction.guild.id));
      const guild = guildResults[0];

      const devSettings = guild?.settings?.devOnDemand;
      if (!devSettings?.enabled) {
        await interaction.reply({
          content: '❌ Developer-on-demand system is not enabled in this server.',
          ephemeral: true,
        });
        return;
      }

      // Check user's active requests
      const userResults = await this.db.select().from(users).where(eq(users.discordId, interaction.user.id));
      let user = userResults[0];

      if (!user) {
        const newUserResults = await this.db.insert(users).values({
          discordId: interaction.user.id,
          username: interaction.user.username,
          preferences: { devRequests: [] },
        } as any).returning();
        user = newUserResults[0];
      }

      const activeRequests = user.preferences?.devRequests?.filter(
        (req: any) => ['open', 'assigned', 'in_progress'].includes(req.status)
      ) || [];

      if (activeRequests.length >= (devSettings.maxActiveRequests || 3)) {
        await interaction.reply({
          content: `❌ You have reached the maximum number of active requests (${devSettings.maxActiveRequests || 3}). Please wait for current requests to complete or cancel them.`,
          ephemeral: true,
        });
        return;
      }

      // Create new request
      const request: DevRequest = {
        id: Date.now().toString(),
        clientId: interaction.user.id,
        clientTag: interaction.user.tag,
        description,
        budget,
        timeline,
        skills: skills ? skills.split(',').map(s => s.trim()) : [],
        status: 'open',
        createdAt: new Date().toISOString(),
      };

      const requests = user.preferences?.devRequests || [];
      requests.push(request);
      const updatedPreferences = { ...user.preferences, devRequests: requests };
      await this.db.update(users).set({ preferences: updatedPreferences } as any).where(eq(users.id, user.id));

      // Post to request channel
      if (devSettings.requestChannel) {
        const channel = interaction.guild.channels.cache.get(devSettings.requestChannel) as TextChannel;
        if (channel) {
          const embed = new EmbedBuilder()
            .setColor(0x10B981)
            .setTitle('🚀 New Developer Request')
            .setDescription(description)
            .addFields([
              { name: '👤 Client', value: interaction.user.tag, inline: true },
              { name: '🆔 Request ID', value: request.id, inline: true },
              { name: '📅 Posted', value: new Date().toLocaleString(), inline: true },
            ]);

          if (budget) embed.addFields([{ name: '💰 Budget', value: budget, inline: true }]);
          if (timeline) embed.addFields([{ name: '⏰ Timeline', value: timeline, inline: true }]);
          if (request.skills.length > 0) {
            embed.addFields([{ name: '🛠️ Required Skills', value: request.skills.join(', '), inline: false }]);
          }

          embed.setFooter({ text: `Use /dev-accept ${request.id} to accept this request` });

          await channel.send({ embeds: [embed] });
        }
      }

      // Notify developers
      if (devSettings.notificationChannel && devSettings.developerRole) {
        const notifyChannel = interaction.guild.channels.cache.get(devSettings.notificationChannel) as TextChannel;
        if (notifyChannel) {
          await notifyChannel.send({
            content: `🔔 <@&${devSettings.developerRole}> New developer request posted! Check the request channel for details.`,
          });
        }
      }

      this.logger.log(`New dev request created by ${interaction.user.tag} in ${interaction.guild.name}: ${request.id}`);

      await interaction.reply({
        content: `✅ **Developer request submitted successfully!**\n\n🆔 **Request ID:** ${request.id}\n📝 **Description:** ${description}\n\nDevelopers will be notified and can accept your request. You'll be contacted when someone is interested!`,
        ephemeral: true,
      });
    } catch (error) {
      this.logger.error('Failed to create dev request:', error);
      await interaction.reply({
        content: '❌ Failed to create developer request. Please try again.',
        ephemeral: true,
      });
    }
  }

  @SlashCommand({
    name: 'dev-browse',
    description: 'Browse available developer requests',
  })
  async onDevBrowseCommand(@Context() [interaction]: SlashCommandContext) {
    if (!interaction.guild) {
      await interaction.reply({
        content: '❌ This command can only be used in a server.',
        ephemeral: true,
      });
      return;
    }

    try {
      const guildResults = await this.db.select().from(guilds).where(eq(guilds.discordId, interaction.guild.id));
      const guild = guildResults[0];

      const devSettings = guild?.settings?.devOnDemand;
      if (!devSettings?.enabled) {
        await interaction.reply({
          content: '❌ Developer-on-demand system is not enabled in this server.',
          ephemeral: true,
        });
        return;
      }

      // Collect all open requests from all users
      const allUsers = await this.db.select().from(users);
      const allRequests: DevRequest[] = [];

      allUsers.forEach(user => {
        const requests = user.preferences?.devRequests?.filter((req: any) => req.status === 'open') || [];
        allRequests.push(...requests);
      });

      if (allRequests.length === 0) {
        await interaction.reply({
          content: '📭 **No open developer requests available.**\n\nCheck back later or encourage clients to post their project needs!',
          ephemeral: true,
        });
        return;
      }

      const embed = new EmbedBuilder()
        .setColor(0x3B82F6)
        .setTitle('👨‍💻 Available Developer Requests')
        .setDescription(`Found **${allRequests.length}** open request(s)`)
        .setTimestamp();

      allRequests
        .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
        .slice(0, 5) // Show latest 5 requests
        .forEach(request => {
          let fieldValue = `**Client:** ${request.clientTag}\n**Description:** ${request.description}`;
          
          if (request.budget) fieldValue += `\n**Budget:** ${request.budget}`;
          if (request.timeline) fieldValue += `\n**Timeline:** ${request.timeline}`;
          if (request.skills.length > 0) fieldValue += `\n**Skills:** ${request.skills.join(', ')}`;
          
          fieldValue += `\n**Posted:** ${new Date(request.createdAt).toLocaleDateString()}`;
          fieldValue += `\n\n*Use \`/dev-accept ${request.id}\` to accept*`;

          embed.addFields([{
            name: `🆔 Request ${request.id}`,
            value: fieldValue,
            inline: false,
          }]);
        });

      if (allRequests.length > 5) {
        embed.setFooter({ text: `Showing latest 5 of ${allRequests.length} requests` });
      }

      await interaction.reply({ embeds: [embed], ephemeral: true });
    } catch (error) {
      this.logger.error('Failed to browse dev requests:', error);
      await interaction.reply({
        content: '❌ Failed to browse requests. Please try again.',
        ephemeral: true,
      });
    }
  }

  @SlashCommand({
    name: 'dev-accept',
    description: 'Accept a developer request',
  })
  async onDevAcceptCommand(
    @Context() [interaction]: SlashCommandContext,
    @Options() { requestId }: DevAcceptDto
  ) {
    if (!interaction.guild) {
      await interaction.reply({
        content: '❌ This command can only be used in a server.',
        ephemeral: true,
      });
      return;
    }

    try {
      // Find the request across all users
      const allUsers = await this.db.select().from(users);
      let targetUser: User | null = null;
      let request: DevRequest | null = null;

      for (const user of allUsers) {
        const req = user.preferences?.devRequests?.find((r: any) => r.id === requestId && r.status === 'open');
        if (req) {
          targetUser = user;
          request = req;
          break;
        }
      }

      if (!request || !targetUser) {
        await interaction.reply({
          content: '❌ Request not found or already assigned. Use `/dev-browse` to see available requests.',
          ephemeral: true,
        });
        return;
      }

      // Update request status
      request.status = 'assigned';
      request.developerId = interaction.user.id;
      request.developerTag = interaction.user.tag;
      request.assignedAt = new Date().toISOString();

      await this.db.update(users).set({ preferences: targetUser.preferences } as any).where(eq(users.id, targetUser.id));

      // Notify client
      try {
        const client = await interaction.guild.members.fetch(request.clientId);
        const embed = new EmbedBuilder()
          .setColor(0x10B981)
          .setTitle('✅ Developer Assigned!')
          .setDescription(`Your project request has been accepted by a developer.`)
          .addFields([
            { name: '👨‍💻 Developer', value: interaction.user.tag },
            { name: '🆔 Request ID', value: request.id },
            { name: '📝 Project', value: request.description },
          ])
          .setFooter({ text: 'The developer will contact you soon to discuss details.' });

        await client.send({ embeds: [embed] });
      } catch {
        // Client might have DMs disabled
      }

      this.logger.log(`Dev request ${requestId} accepted by ${interaction.user.tag} in ${interaction.guild.name}`);

      await interaction.reply({
        content: `✅ **Request accepted successfully!**\n\n🆔 **Request ID:** ${requestId}\n👤 **Client:** ${request.clientTag}\n📝 **Project:** ${request.description}\n\nThe client has been notified. Please reach out to them to discuss project details and next steps.`,
        ephemeral: true,
      });
    } catch (error) {
      this.logger.error('Failed to accept dev request:', error);
      await interaction.reply({
        content: '❌ Failed to accept request. Please try again.',
        ephemeral: true,
      });
    }
  }

  @SlashCommand({
    name: 'dev-requests',
    description: 'View your active developer requests',
  })
  async onDevRequestsCommand(@Context() [interaction]: SlashCommandContext) {
    try {
      const userResults = await this.db.select().from(users).where(eq(users.discordId, interaction.user.id));
      const user = userResults[0];

      const requests = user?.preferences?.devRequests?.filter(
        (req: any) => ['open', 'assigned', 'in_progress'].includes(req.status)
      ) || [];

      if (requests.length === 0) {
        await interaction.reply({
          content: '📭 **You have no active developer requests.**\n\nUse `/dev-request` to create a new request.',
          ephemeral: true,
        });
        return;
      }

      const embed = new EmbedBuilder()
        .setColor(0x3B82F6)
        .setTitle('📋 Your Active Developer Requests')
        .setDescription(`You have **${requests.length}** active request(s)`);

      requests.forEach((request: any) => {
        let statusEmoji = '🔍';
        let statusText = 'Open';
        
        if (request.status === 'assigned') {
          statusEmoji = '✅';
          statusText = `Assigned to ${request.developerTag}`;
        } else if (request.status === 'in_progress') {
          statusEmoji = '⚡';
          statusText = `In Progress - ${request.developerTag}`;
        }

        let fieldValue = `**Status:** ${statusEmoji} ${statusText}\n**Description:** ${request.description}`;
        if (request.budget) fieldValue += `\n**Budget:** ${request.budget}`;
        if (request.timeline) fieldValue += `\n**Timeline:** ${request.timeline}`;
        fieldValue += `\n**Created:** ${new Date(request.createdAt).toLocaleDateString()}`;

        embed.addFields([{
          name: `🆔 Request ${request.id}`,
          value: fieldValue,
          inline: false,
        }]);
      });

      await interaction.reply({ embeds: [embed], ephemeral: true });
    } catch (error) {
      this.logger.error('Failed to get user requests:', error);
      await interaction.reply({
        content: '❌ Failed to retrieve your requests. Please try again.',
        ephemeral: true,
      });
    }
  }
}