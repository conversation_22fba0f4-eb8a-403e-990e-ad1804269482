import { Inject, Injectable, Logger } from '@nestjs/common';
import { User as DiscordUser } from 'discord.js';
import { eq } from 'drizzle-orm';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { Context, Options, SlashCommand, SlashCommandContext, UserOption } from 'necord';
import { DATABASE_CONNECTION } from '../../core/database/database.service';
import { users } from '../../core/database/schema';

class BalanceDto {
  @UserOption({ name: 'user', description: 'Select a user to check their coin balance (leave empty for yourself)', required: false })
  user?: DiscordUser;
}

@Injectable()
export class EconomyService {
  private readonly logger = new Logger(EconomyService.name);

  constructor(
    @Inject(DATABASE_CONNECTION) private readonly db: NodePgDatabase,
  ) {}

  @SlashCommand({ name: 'balance', description: '💰 Check coin balance for yourself or another user' })
  async onBalanceCommand(@Context() [interaction]: SlashCommandContext, @Options() options: BalanceDto) {
    try {
      const targetUser = options.user || interaction.user;
      const userResults = await this.db.select().from(users).where(eq(users.discordId, targetUser.id));
      const user = userResults[0];
      const balance = user?.balance || 0;
      const isOwnBalance = targetUser === interaction.user;
      
      await interaction.reply({
        content: `💰 **${isOwnBalance ? 'Your' : `${targetUser.tag}'s`} Balance**\n\n🪙 Coins: **${balance}**${isOwnBalance ? '\n\nEarn coins by participating in the community! 🎯' : ''}`,
        ephemeral: true,
      });
      this.logger.log(`Balance checked: ${targetUser.tag} (${balance} coins) by ${interaction.user.tag}`);
    } catch (error) {
      this.logger.error('Balance command failed:', error);
      await interaction.reply({ content: '❌ Failed to get balance information.', ephemeral: true });
    }
  }

  @SlashCommand({
    name: 'daily',
    description: '🎁 Claim your daily coin reward (resets every 24 hours)',
  })
  // @ts-ignore: Necord decorator compatibility issue
  async onDailyCommand(@Context() [interaction]: SlashCommandContext) {
    try {
      const userResults = await this.db.select().from(users).where(eq(users.discordId, interaction.user.id));
      let user = userResults[0];

      if (!user) {
        const newUserResults = await this.db.insert(users).values({
          discordId: interaction.user.id,
          username: interaction.user.username,
          balance: 0,
          preferences: { lastDaily: null, dailyStreak: 0 },
        } as any).returning();
        user = newUserResults[0];
      }

      const now = new Date();
      const lastDaily = user.preferences?.lastDaily ? new Date(user.preferences.lastDaily) : null;
      
      // Check if user already claimed today
      if (lastDaily && this.isSameDay(now, lastDaily)) {
        const nextDaily = new Date(lastDaily);
        nextDaily.setDate(nextDaily.getDate() + 1);
        nextDaily.setHours(0, 0, 0, 0);
        
        await interaction.reply({
          content: `🎁 You've already claimed your daily reward today!\n⏰ Next daily available: <t:${Math.floor(nextDaily.getTime() / 1000)}:R>`,
          ephemeral: true,
        });
        return;
      }

      // Calculate streak
      let streak = user.preferences?.dailyStreak || 0;
      if (lastDaily && this.isConsecutiveDay(now, lastDaily)) {
        streak += 1;
      } else {
        streak = 1; // Reset or start streak
      }

      // Calculate reward based on streak (base 100, bonus for streak)
      const baseReward = 100;
      const streakBonus = Math.min(streak * 25, 500); // Max 500 bonus at 20 day streak
      const totalReward = baseReward + streakBonus;

      // Update user
      const newBalance = user.balance + totalReward;
      const updatedPreferences = {
        ...user.preferences,
        lastDaily: now.toISOString(),
        dailyStreak: streak,
      };

      await this.db.update(users).set({
        balance: newBalance,
        preferences: updatedPreferences
      } as any).where(eq(users.id, user.id));
      
      // Update local user object for response
      user.balance = newBalance;

      this.logger.log(`${interaction.user.tag} claimed daily reward: ${totalReward} coins (streak: ${streak})`);

      let rewardMessage = `🎁 **Daily Reward Claimed!**\n\n💰 **+${totalReward} coins**\n🪙 **New balance:** ${user.balance} coins`;

      if (streak > 1) {
        rewardMessage += `\n🔥 **${streak} day streak!** (+${streakBonus} bonus coins)`;
      }

      if (streak >= 7) {
        rewardMessage += `\n🏆 **Amazing streak! Keep it up!**`;
      }

      await interaction.reply({
        content: rewardMessage,
        ephemeral: true,
      });
    } catch (error) {
      this.logger.error('Daily command failed:', error);
      await interaction.reply({
        content: '❌ Failed to claim daily reward.',
        ephemeral: true,
      });
    }
  }

  private isSameDay(date1: Date, date2: Date): boolean {
    return date1.getFullYear() === date2.getFullYear() &&
           date1.getMonth() === date2.getMonth() &&
           date1.getDate() === date2.getDate();
  }

  private isConsecutiveDay(today: Date, lastDaily: Date): boolean {
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    return this.isSameDay(yesterday, lastDaily);
  }

  @SlashCommand({
    name: 'shop',
    description: '🛒 Browse and purchase items from the server shop',
  })
  // @ts-ignore: Necord decorator compatibility issue
  async onShopCommand(@Context() [interaction]: SlashCommandContext) {
    await interaction.reply({
      content: '🛒 Server shop is coming soon! Exciting items await.',
      ephemeral: true,
    });
  }
}