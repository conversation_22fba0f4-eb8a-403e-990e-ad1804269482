import { Module } from '@nestjs/common';
import { WelcomeModule } from './welcome/welcome.module';
import { MusicModule } from './music/music.module';
import { GamingModule } from './gaming/gaming.module';
import { LevelingModule } from './leveling/leveling.module';
import { EconomyModule } from './economy/economy.module';
import { ModerationModule } from './moderation/moderation.module';
import { UtilityModule } from './utility/utility.module';
import { StarboardModule } from './starboard/starboard.module';
import { RoleAccessModule } from './role-access/role-access.module';
import { DevOnDemandModule } from './dev-on-demand/dev-on-demand.module';
import { AIAutomationModule } from './ai-automation/ai-automation.module';

@Module({
  imports: [
    WelcomeModule,
    MusicModule,
    GamingModule,
    LevelingModule,
    EconomyModule,
    ModerationModule,
    UtilityModule,
    StarboardModule,
    RoleAccessModule,
    DevOnDemandModule,
    AIAutomationModule,
  ],
})
export class FeaturesModule {}