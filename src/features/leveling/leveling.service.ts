import { Inject, Injectable, Logger } from '@nestjs/common';
import { User as DiscordUser, Message } from 'discord.js';
import { eq } from 'drizzle-orm';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { Context, On, Options, SlashCommand, SlashCommandContext, UserOption } from 'necord';
import { DATABASE_CONNECTION } from '../../core/database/database.service';
import { users } from '../../core/database/schema';

class LevelDto {
  @UserOption({ name: 'user', description: 'User to check level for', required: false })
  user?: DiscordUser;
}

@Injectable()
export class LevelingService {
  private readonly logger = new Logger(LevelingService.name);
  private readonly xpCooldowns = new Map<string, number>();

  constructor(
    @Inject(DATABASE_CONNECTION) private readonly db: NodePgDatabase,
  ) {}

  @On('messageCreate')
  async handleMessage(@Context() [message]: [Message]) {
    if (message.author.bot) return;

    const userId = message.author.id;
    const now = Date.now();
    const cooldown = this.xpCooldowns.get(userId);

    // 1 minute cooldown for XP gain
    if (cooldown && now - cooldown < 60000) return;

    this.xpCooldowns.set(userId, now);
    await this.addXP(userId, this.getRandomXP());
  }

  @SlashCommand({ name: 'level', description: 'Check current level and XP (yours or another user\'s)' })
  async onLevelCommand(@Context() [interaction]: SlashCommandContext, @Options() options: LevelDto) {
    try {
      const targetUser = options.user || interaction.user;
      const userResults = await this.db.select().from(users).where(eq(users.discordId, targetUser.id));
      const user = userResults[0];
      const isOwnLevel = targetUser === interaction.user;

      if (!user || !user.experience) {
        await interaction.reply({
          content: `📊 ${isOwnLevel ? 'You need' : `${targetUser.tag} needs`} to send some messages first to start gaining XP!`,
          ephemeral: true,
        });
        return;
      }

      const level = this.calculateLevel(user.experience);
      const xpForNext = this.getXPForLevel(level + 1);
      const xpForCurrent = this.getXPForLevel(level);
      const progress = user.experience - xpForCurrent;
      const needed = xpForNext - xpForCurrent;

      await interaction.reply({
        content: `📊 **${isOwnLevel ? 'Your' : `${targetUser.tag}'s`} Level Stats**\n\n🎯 Level: **${level}**\n⭐ Total XP: **${user.experience}**\n📈 Progress: **${progress}/${needed}** XP to next level${isOwnLevel ? '\n\nKeep chatting to gain more XP! 🚀' : ''}`,
        ephemeral: true,
      });
      this.logger.log(`Level checked: ${targetUser.tag} (level ${level}) by ${interaction.user.tag}`);
    } catch (error) {
      this.logger.error('Level command failed:', error);
      await interaction.reply({ content: '❌ Failed to get level information.', ephemeral: true });
    }
  }

  @SlashCommand({ name: 'rank', description: 'View server leaderboard or specific user rank' })
  async onRankCommand(@Context() [interaction]: SlashCommandContext, @Options() options: LevelDto) {
    const targetUser = options.user;
    await interaction.reply({
      content: `🏆 Server leaderboard${targetUser ? ` and ${targetUser.tag}'s rank` : ''} is coming soon! Keep gaining XP.`,
      ephemeral: true,
    });
  }

  private async addXP(userId: string, amount: number) {
    try {
      const userResults = await this.db.select().from(users).where(eq(users.discordId, userId));
      let user = userResults[0];

      if (!user) {
        await this.db.insert(users).values({
          discordId: userId,
          username: 'Unknown',
          experience: amount,
        } as any);
      } else {
        const oldLevel = this.calculateLevel(user.experience);
        const newExperience = user.experience + amount;
        const newLevel = this.calculateLevel(newExperience);

        // Level up notification would go here
        if (newLevel > oldLevel) {
          this.logger.log(`User ${userId} leveled up to ${newLevel}!`);
        }

        await this.db.update(users).set({ experience: newExperience } as any).where(eq(users.id, user.id));
      }
    } catch (error) {
      this.logger.error(`Failed to add XP to user ${userId}:`, error);
    }
  }

  private getRandomXP(): number {
    return Math.floor(Math.random() * 15) + 5; // 5-20 XP
  }

  private calculateLevel(xp: number): number {
    return Math.floor(Math.sqrt(xp / 100));
  }

  private getXPForLevel(level: number): number {
    return level * level * 100;
  }
}