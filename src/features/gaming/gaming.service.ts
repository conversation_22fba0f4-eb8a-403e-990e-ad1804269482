import { Injectable, Logger } from '@nestjs/common';
import { SlashCommand, SlashCommandContext, Context } from 'necord';

@Injectable()
export class GamingService {
  private readonly logger = new Logger(GamingService.name);

  @SlashCommand({
    name: 'game',
    description: 'Start a quick mini-game or challenge',
  })
  // @ts-ignore: Necord decorator compatibility issue
  async onGameCommand(@Context() [interaction]: SlashCommandContext) {
    await interaction.reply({
      content: '🎮 Gaming features are being developed! Exciting games coming soon.',
      ephemeral: true,
    });
  }

  @SlashCommand({
    name: 'leaderboard',
    description: 'View gaming leaderboards',
  })
  // @ts-ignore: Necord decorator compatibility issue
  async onLeaderboardCommand(@Context() [interaction]: SlashCommandContext) {
    await interaction.reply({
      content: '🏆 Gaming leaderboards are coming soon!',
      ephemeral: true,
    });
  }
}