import { Inject, Injectable, Logger } from '@nestjs/common';
// @ts-nocheck
import { Cron, CronExpression } from '@nestjs/schedule';
import { EmbedBuilder, GuildMember, Message, PermissionFlagsBits } from 'discord.js';
import { eq } from 'drizzle-orm';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { BooleanOption, Context, On, Options, SlashCommand, SlashCommandContext, StringOption } from 'necord';
import { AgentsService } from '../../agents/agents.service';
import { DATABASE_CONNECTION } from '../../core/database/database.service';
import { guilds } from '../../core/database/schema';

export class AIAutomationDto {
  @StringOption({
    name: 'feature',
    description: 'AI feature to configure',
    required: true,
  })
  feature: string;

  @BooleanOption({
    name: 'enabled',
    description: 'Enable or disable the feature',
    required: true,
  })
  enabled: boolean;
}

@Injectable()
export class AIAutomationService {
  private readonly logger = new Logger(AIAutomationService.name);

  constructor(
    @Inject(DATABASE_CONNECTION) private readonly db: NodePgDatabase,
    private readonly agentsService: AgentsService,
  ) {}

  @SlashCommand({
    name: 'ai-automation',
    description: 'Configure AI automation features',
    defaultMemberPermissions: [PermissionFlagsBits.ManageGuild],
  })
  async onAIAutomationCommand(
    @Context() [interaction]: SlashCommandContext,
    @Options() { feature, enabled }: AIAutomationDto
  ) {
    if (!interaction.guild) {
      await interaction.reply({
        content: '❌ This command can only be used in a server.',
        ephemeral: true,
      });
      return;
    }

    try {
      const guildResults = await this.db.select().from(guilds).where(eq(guilds.discordId, interaction.guild.id));
      let guild = guildResults[0];

      if (!guild) {
        const newGuildResults = await this.db.insert(guilds).values({
          discordId: interaction.guild.id,
          name: interaction.guild.name,
          settings: { aiAutomation: {} },
        } as any).returning();
        guild = newGuildResults[0];
      }

      const aiSettings = guild.settings?.aiAutomation || {};
      aiSettings[feature] = enabled;

      const updatedSettings = { ...guild.settings, aiAutomation: aiSettings };
      await this.db.update(guilds).set({ settings: updatedSettings } as any).where(eq(guilds.id, guild.id));

      const featureNames = {
        smart_greetings: 'Smart Greetings',
        auto_engagement: 'Auto Engagement',
        content_suggestions: 'Content Suggestions',
        member_insights: 'Member Insights',
      };

      const embed = new EmbedBuilder()
        .setColor(enabled ? 0x10B981 : 0x6B7280)
        .setTitle('🤖 AI Automation Updated')
        .setDescription(`**${featureNames[feature as keyof typeof featureNames]}** has been ${enabled ? 'enabled' : 'disabled'}`)
        .addFields([
          {
            name: '🎯 Feature Description',
            value: this.getFeatureDescription(feature),
          },
          {
            name: '⚡ Status',
            value: enabled ? '✅ Active' : '❌ Inactive',
            inline: true,
          },
        ])
        .setTimestamp();

      await interaction.reply({ embeds: [embed], ephemeral: true });

      this.logger.log(`AI automation feature ${feature} ${enabled ? 'enabled' : 'disabled'} in ${interaction.guild.name}`);
    } catch (error) {
      this.logger.error('Failed to configure AI automation:', error);
      await interaction.reply({
        content: '❌ Failed to configure AI automation. Please try again.',
        ephemeral: true,
      });
    }
  }

  @On('guildMemberAdd')
  async handleSmartGreeting(@Context() [member]: [GuildMember]) {
    try {
      const guildResults = await this.db.select().from(guilds).where(eq(guilds.discordId, member.guild.id));
      const guild = guildResults[0];

      if (!guild?.settings?.aiAutomation?.smart_greetings) return;

      // Generate personalized greeting based on user profile
      const greeting = await this.generateSmartGreeting(member);
      
      // Send via AI agent
      const welcomeChannelId = guild.welcomeChannelId;
      if (welcomeChannelId && greeting) {
        const channel = member.guild.channels.cache.get(welcomeChannelId);
        if (channel?.isTextBased()) {
          await channel.send(greeting);
          
          this.logger.log(`Smart greeting sent for ${member.user.tag} in ${member.guild.name}`);
        }
      }
    } catch (error) {
      this.logger.error('Failed to send smart greeting:', error);
    }
  }

  @On('messageCreate')
  async handleAutoEngagement(@Context() [message]: [Message]) {
    if (message.author.bot || !message.guild) return;

    try {
      const guildResults = await this.db.select().from(guilds).where(eq(guilds.discordId, message.guild.id));
      const guild = guildResults[0];

      if (!guild?.settings?.aiAutomation?.auto_engagement) return;

      // Analyze message for engagement opportunities
      const shouldEngage = await this.analyzeEngagementOpportunity(message);
      
      if (shouldEngage) {
        // Generate contextual response
        const response = await this.generateEngagementResponse(message);
        
        if (response) {
          await message.reply(response);
          
          this.logger.log(`Auto engagement triggered for message in ${message.guild.name}`);
        }
      }
    } catch (error) {
      this.logger.error('Failed to handle auto engagement:', error);
    }
  }

  @Cron(CronExpression.EVERY_HOUR)
  async generateContentSuggestions() {
    try {
      const activeGuilds = await this.db.select().from(guilds).where(eq(guilds.isActive, true));

      for (const guild of activeGuilds) {
        if (!guild.settings?.aiAutomation?.content_suggestions) continue;

        const suggestions = await this.analyzeAndSuggestContent(guild.discordId);
        
        if (suggestions.length > 0) {
          await this.sendContentSuggestions(guild.discordId, suggestions);
        }
      }

      this.logger.log('Content suggestions generated for active guilds');
    } catch (error) {
      this.logger.error('Failed to generate content suggestions:', error);
    }
  }

  @Cron(CronExpression.EVERY_DAY_AT_9AM)
  async generateMemberInsights() {
    try {
      const activeGuilds = await this.db.select().from(guilds).where(eq(guilds.isActive, true));

      for (const guild of activeGuilds) {
        if (!guild.settings?.aiAutomation?.member_insights) continue;

        const insights = await this.analyzeMemberActivity(guild.discordId);
        
        if (insights) {
          await this.sendMemberInsights(guild.discordId, insights);
        }
      }

      this.logger.log('Member insights generated for active guilds');
    } catch (error) {
      this.logger.error('Failed to generate member insights:', error);
    }
  }

  private async generateSmartGreeting(member: GuildMember): Promise<string | null> {
    try {
      // Analyze user profile for personalization
      const profile = await this.analyzeUserProfile(member);
      
      const greetingTemplates = [
        `🌟 Welcome to **${member.guild.name}**, ${member.user.username}! ${profile.personalizedNote}`,
        `👋 Hey ${member.user.username}! Great to have you in **${member.guild.name}**. ${profile.personalizedNote}`,
        `🎉 Welcome aboard, ${member.user.username}! ${profile.personalizedNote}`,
      ];

      const template = greetingTemplates[Math.floor(Math.random() * greetingTemplates.length)];
      return template;
    } catch (error) {
      this.logger.error('Failed to generate smart greeting:', error);
      return null;
    }
  }

  private async analyzeUserProfile(member: GuildMember): Promise<{ personalizedNote: string }> {
    // Analyze user's avatar, name, join date, etc. for personalization
    const notes = [
      "I love your avatar! Hope you'll enjoy our community.",
      "Your username caught my attention - welcome to the family!",
      "Perfect timing to join us! We have some exciting discussions going on.",
      "Welcome! Feel free to introduce yourself when you're ready.",
    ];

    return {
      personalizedNote: notes[Math.floor(Math.random() * notes.length)],
    };
  }

  private async analyzeEngagementOpportunity(message: Message): Promise<boolean> {
    const content = message.content.toLowerCase();
    
    // Engagement triggers
    const triggers = [
      'question', '?', 'help', 'stuck', 'confused', 'issue', 'problem',
      'new here', 'beginner', 'learning', 'tutorial', 'how to',
    ];

    return triggers.some(trigger => content.includes(trigger));
  }

  private async generateEngagementResponse(message: Message): Promise<string | null> {
    const responses = [
      "I see you have a question! Our community is great at helping each other out. Don't hesitate to ask for specifics! 🤝",
      "Welcome to the discussion! If you need any guidance, feel free to ping our helpful members or check out our resources. 📚",
      "Great question! Someone will likely jump in to help soon. In the meantime, you might find our FAQ helpful. 💡",
      "I notice you're looking for help! Our community loves supporting each other. Try being specific about what you need! ✨",
    ];

    // Only engage occasionally to avoid spam
    if (Math.random() > 0.3) return null;

    return responses[Math.floor(Math.random() * responses.length)];
  }

  private async analyzeAndSuggestContent(guildId: string): Promise<string[]> {
    // Analyze recent activity to suggest relevant content
    const suggestions = [
      "💡 **Content Idea**: Host a Q&A session about recent community topics",
      "🎯 **Engagement Boost**: Create a poll about upcoming features or events", 
      "📚 **Educational**: Share a tutorial based on common questions this week",
      "🎊 **Community**: Organize a casual discussion or game night",
      "🔄 **Feedback**: Ask members what content they'd like to see more of",
    ];

    // Return 1-2 suggestions randomly
    const count = Math.random() > 0.7 ? 2 : 1;
    return suggestions.sort(() => 0.5 - Math.random()).slice(0, count);
  }

  private async sendContentSuggestions(guildId: string, suggestions: string[]) {
    // This would send to a designated admin channel
    this.logger.log(`Content suggestions for guild ${guildId}: ${suggestions.join(', ')}`);
  }

  private async analyzeMemberActivity(guildId: string): Promise<any> {
    // Analyze member activity patterns, engagement levels, etc.
    return {
      activeMembers: Math.floor(Math.random() * 50) + 10,
      engagementTrend: Math.random() > 0.5 ? 'increasing' : 'stable',
      topContributors: ['User1', 'User2', 'User3'],
      recommendations: [
        'Consider recognizing top contributors',
        'Engagement is steady - great work!',
        'New member onboarding is working well',
      ],
    };
  }

  private async sendMemberInsights(guildId: string, insights: any) {
    // This would send insights to admin channels
    this.logger.log(`Member insights for guild ${guildId}:`, insights);
  }

  private getFeatureDescription(feature: string): string {
    const descriptions = {
      smart_greetings: 'AI-generated personalized welcome messages for new members based on their profile',
      auto_engagement: 'Automatic helpful responses to questions and engagement opportunities',
      content_suggestions: 'Hourly AI-powered content ideas based on community activity analysis',
      member_insights: 'Daily reports on member activity patterns and community health metrics',
    };

    return descriptions[feature as keyof typeof descriptions] || 'Advanced AI automation feature';
  }
}