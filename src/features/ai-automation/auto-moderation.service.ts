import { Injectable, Logger, Inject } from '@nestjs/common';
import { On } from 'necord';
import { Message, GuildMember, EmbedBuilder } from 'discord.js';
import { DATABASE_CONNECTION } from '../../core/database/database.service';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { eq } from 'drizzle-orm';
import { guilds, users, Guild, User } from '../../core/database/schema';

@Injectable()
export class AutoModerationService {
  private readonly logger = new Logger(AutoModerationService.name);
  
  // Simple content filters (in production, use ML models)
  private readonly toxicPatterns = [
    /\b(spam|scam|free money|click here|buy now)\b/gi,
    /(.)\1{4,}/g, // Repeated characters
    /[A-Z]{5,}/g, // Excessive caps
  ];

  private readonly suspiciousPatterns = [
    /discord\.gg\/[a-zA-Z0-9]+/g, // Discord invites
    /https?:\/\/[^\s]+/g, // URLs (check against whitelist)
  ];

  constructor(
    @Inject(DATABASE_CONNECTION) private readonly db: NodePgDatabase,
  ) {}

  @On('messageCreate')
  async handleAutoModeration(message: Message) {
    if (!message || !message.author || message.author.bot || !message.guild) return;

    try {
      const guildResults = await this.db.select().from(guilds).where(eq(guilds.discordId, message.guild.id));
      const guild = guildResults[0];

      if (!guild?.settings?.aiAutomation?.auto_moderation) return;

      const analysis = await this.analyzeMessage(message);
      
      if (analysis.shouldModerate) {
        await this.takeAction(message, analysis);
      }
    } catch (error) {
      this.logger.error('Auto moderation failed:', error);
    }
  }

  private async analyzeMessage(message: Message): Promise<{
    shouldModerate: boolean;
    reason: string;
    severity: 'low' | 'medium' | 'high';
    action: 'warn' | 'delete' | 'timeout' | 'flag';
  }> {
    const content = message.content;
    
    // Check for toxic patterns
    for (const pattern of this.toxicPatterns) {
      if (pattern.test(content)) {
        return {
          shouldModerate: true,
          reason: 'Potential spam or inappropriate content detected',
          severity: 'medium',
          action: 'delete',
        };
      }
    }

    // Check for suspicious patterns
    for (const pattern of this.suspiciousPatterns) {
      if (pattern.test(content)) {
        return {
          shouldModerate: true,
          reason: 'Suspicious link or invite detected',
          severity: 'low',
          action: 'flag',
        };
      }
    }

    // Check user behavior patterns
    const userBehavior = await this.analyzeUserBehavior(message.author.id, message.guild!.id);
    if (userBehavior.suspicious) {
      return {
        shouldModerate: true,
        reason: 'User behavior pattern flagged',
        severity: 'medium',
        action: 'warn',
      };
    }

    return {
      shouldModerate: false,
      reason: '',
      severity: 'low',
      action: 'warn',
    };
  }

  private async analyzeUserBehavior(userId: string, guildId: string): Promise<{
    suspicious: boolean;
    reasons: string[];
  }> {
    // Analyze user's recent activity patterns
    const reasons = [];
    
    // Check if user is very new (potential raid account)
    const userResults = await this.db.select().from(users).where(eq(users.discordId, userId));
    const user = userResults[0];
    
    if (user && user.createdAt) {
      const accountAge = Date.now() - new Date(user.createdAt).getTime();
      if (accountAge < 24 * 60 * 60 * 1000) { // Less than 24 hours
        reasons.push('Very new account');
      }
    }

    // In production, would check:
    // - Message frequency
    // - Similar content patterns
    // - User report history
    // - Cross-server behavior

    return {
      suspicious: reasons.length > 0,
      reasons,
    };
  }

  private async takeAction(message: Message, analysis: any) {
    switch (analysis.action) {
      case 'delete':
        await this.deleteMessage(message, analysis.reason);
        break;
      case 'warn':
        await this.warnUser(message, analysis.reason);
        break;
      case 'timeout':
        await this.timeoutUser(message, analysis.reason);
        break;
      case 'flag':
        await this.flagForReview(message, analysis.reason);
        break;
    }
  }

  private async deleteMessage(message: Message, reason: string) {
    try {
      await message.delete();
      
      // Send warning to user
      const embed = new EmbedBuilder()
        .setColor(0xFF6B35)
        .setTitle('⚠️ Message Removed')
        .setDescription('Your message was automatically removed by our moderation system.')
        .addFields([
          { name: 'Reason', value: reason },
          { name: 'Channel', value: `${message.channel}` },
        ])
        .setFooter({ text: 'If you think this was a mistake, please contact a moderator.' });

      try {
        await message.author.send({ embeds: [embed] });
      } catch {
        // User has DMs disabled
      }

      this.logger.log(`Auto-deleted message from ${message.author.tag} in ${message.guild!.name}: ${reason}`);
    } catch (error) {
      this.logger.error('Failed to delete message:', error);
    }
  }

  private async warnUser(message: Message, reason: string) {
    try {
      const embed = new EmbedBuilder()
        .setColor(0xFBBF24)
        .setTitle('⚠️ Automated Warning')
        .setDescription('Our AI moderation system has flagged your recent activity.')
        .addFields([
          { name: 'Reason', value: reason },
          { name: 'Channel', value: `${message.channel}` },
          { name: 'Action Required', value: 'Please review your messages and follow server rules.' },
        ])
        .setFooter({ text: 'Repeated violations may result in automatic penalties.' });

      try {
        await message.author.send({ embeds: [embed] });
      } catch {
        // Fallback to channel warning
        const warning = await message.reply({
          content: `⚠️ ${message.author}, please review your message. Reason: ${reason}`,
        });
        
        // Delete warning after 10 seconds
        setTimeout(() => warning.delete().catch(() => {}), 10000);
      }

      this.logger.log(`Auto-warned user ${message.author.tag} in ${message.guild!.name}: ${reason}`);
    } catch (error) {
      this.logger.error('Failed to warn user:', error);
    }
  }

  private async timeoutUser(message: Message, reason: string) {
    try {
      const member = message.member;
      if (!member || !member.moderatable) return;

      const timeoutDuration = 5 * 60 * 1000; // 5 minutes
      await member.timeout(timeoutDuration, `Auto-moderation: ${reason}`);

      const embed = new EmbedBuilder()
        .setColor(0xEF4444)
        .setTitle('🔇 Automatic Timeout')
        .setDescription('You have been temporarily muted by our moderation system.')
        .addFields([
          { name: 'Reason', value: reason },
          { name: 'Duration', value: '5 minutes' },
          { name: 'Server', value: message.guild!.name },
        ])
        .setFooter({ text: 'Please follow server rules to avoid further action.' });

      try {
        await message.author.send({ embeds: [embed] });
      } catch {
        // User has DMs disabled
      }

      this.logger.log(`Auto-timed out user ${message.author.tag} in ${message.guild!.name}: ${reason}`);
    } catch (error) {
      this.logger.error('Failed to timeout user:', error);
    }
  }

  private async flagForReview(message: Message, reason: string) {
    try {
      // Send to moderation log channel
      const guildResults = await this.db.select().from(guilds).where(eq(guilds.discordId, message.guild!.id));
      const guild = guildResults[0];

      const logChannelId = guild?.settings?.moderation?.logChannel;
      if (logChannelId) {
        const logChannel = message.guild!.channels.cache.get(logChannelId);
        if (logChannel?.isTextBased()) {
          const embed = new EmbedBuilder()
            .setColor(0xFBBF24)
            .setTitle('🚩 Auto-Moderation Flag')
            .setDescription('A message has been flagged for moderator review.')
            .addFields([
              { name: 'User', value: `${message.author.tag} (${message.author.id})` },
              { name: 'Channel', value: `${message.channel}` },
              { name: 'Reason', value: reason },
              { name: 'Message Content', value: message.content.substring(0, 1000) },
              { name: 'Message Link', value: `[Jump to Message](${message.url})` },
            ])
            .setTimestamp();

          await logChannel.send({ embeds: [embed] });
        }
      }

      this.logger.log(`Flagged message from ${message.author.tag} in ${message.guild!.name}: ${reason}`);
    } catch (error) {
      this.logger.error('Failed to flag message:', error);
    }
  }
}