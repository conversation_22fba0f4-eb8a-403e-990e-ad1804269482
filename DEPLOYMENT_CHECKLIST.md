# Sevalla Deployment Checklist

## Pre-Deployment Setup

### 1. <PERSON><PERSON><PERSON> Account Setup
- [ ] Create Sevalla account
- [ ] Connect GitHub repository
- [ ] Generate API key for deployments

### 2. Environment Configuration
- [ ] Set up environment variables in Sevalla dashboard:
  - [ ] `BOT_CLIENT_ID`
  - [ ] `BOT_CLIENT_SECRET`
  - [ ] `NEXT_PUBLIC_BOT_CLIENT_ID`
  - [ ] `APP_URL`
  - [ ] `NEXT_PUBLIC_API_ENDPOINT`
  - [ ] `INTERNAL_API_ENDPOINT`
  - [ ] `NODE_ENV=production`
  - [ ] `PORT=3000`
  - [ ] `ENABLE_ENV_LOGIN=false`
  - [ ] `NEXT_TELEMETRY_DISABLED=1`

### 3. GitHub Secrets (if using GitHub Actions)
- [ ] `SEVALLA_API_KEY`
- [ ] `SEVALLA_PROJECT_ID`
- [ ] `SEVALLA_DEPLOYMENT_URL`

## Deployment Configuration

### 4. Application Settings
- [ ] Build Command: `pnpm run build`
- [ ] Start Command: `pnpm start`
- [ ] Port: `3000`
- [ ] Health Check Path: `/api/health`
- [ ] Enable zero-downtime deployments

### 5. Pipeline Configuration
- [ ] Set up trunk-based development pipeline
- [ ] Configure automatic deployments for development/staging
- [ ] Set manual deployment for production
- [ ] Enable preview apps for pull requests

### 6. Performance Optimizations
- [ ] Enable CDN
- [ ] Enable edge caching
- [ ] Configure build cache
- [ ] Set up health check monitoring

## Testing & Validation

### 7. Local Testing
- [ ] Build application locally: `pnpm run build`
- [ ] Start application: `pnpm start`
- [ ] Test health endpoint: `curl http://localhost:3000/api/health`
- [ ] Verify all pages load correctly

### 8. Deployment Testing
- [ ] Deploy to development environment
- [ ] Verify health check passes
- [ ] Test all application features
- [ ] Check environment variables are loaded
- [ ] Test authentication flow

### 9. Production Deployment
- [ ] Deploy to staging first
- [ ] Run integration tests
- [ ] Verify performance metrics
- [ ] Deploy to production
- [ ] Monitor logs for errors

## Post-Deployment

### 10. Monitoring
- [ ] Set up application monitoring
- [ ] Configure alerts for health check failures
- [ ] Monitor application performance
- [ ] Set up log aggregation

### 11. Documentation
- [ ] Update deployment documentation
- [ ] Document any environment-specific configurations
- [ ] Update team on deployment process
- [ ] Create incident response plan

## Rollback Plan

### 12. Emergency Procedures
- [ ] Document rollback procedure
- [ ] Test rollback in staging
- [ ] Prepare communication plan
- [ ] Set up monitoring alerts

## Files Created/Modified

### Configuration Files
- [x] `sevalla.json` - Main Sevalla configuration
- [x] `zeabur.json` - Updated with health check
- [x] `.env.sevalla` - Production environment template
- [x] `src/pages/api/health.ts` - Health check endpoint

### Documentation
- [x] `SEVALLA_DEPLOYMENT.md` - Deployment guide
- [x] `DEPLOYMENT_CHECKLIST.md` - This checklist

### CI/CD
- [ ] `.github/workflows/sevalla-deploy.yml` - GitHub Actions workflow (needs manual creation due to permissions)

### Package Configuration
- [x] `package.json` - Added Sevalla-specific scripts
- [x] `next.config.js` - Added production optimizations