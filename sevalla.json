{"name": "discord-bot-dashboard-backend", "version": "0.0.1", "build": {"command": "pnpm run build", "path": ".", "cache": true}, "deployment": {"type": "application", "environment": "nodejs", "framework": "<PERSON><PERSON><PERSON>", "port": 8080, "healthCheck": {"path": "/health", "timeout": 30, "interval": 10, "retries": 3}, "autoDeployment": true, "zeroDowntime": true}, "processes": {"web": {"command": "pnpm run start:prod", "healthCheck": "/health"}}, "pipeline": {"type": "trunk-based", "stages": [{"name": "development", "branch": "backend", "autoDeployment": true}, {"name": "staging", "branch": "staging", "autoDeployment": true}, {"name": "production", "branch": "master", "autoDeployment": false}]}, "features": {"previewApps": true, "edgeCaching": true, "cdn": true}}