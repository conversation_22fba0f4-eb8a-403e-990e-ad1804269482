# Internal Network Communication

This feature enables communication between different branch deployments of the Discord bot, allowing for data synchronization and network monitoring across environments.

## Configuration

Set these environment variables:

```bash
# Required: Internal API endpoint URL
INTERNAL_API_ENDPOINT="http://your-internal-service-url:8080"

# Optional: Branch name for identification (defaults to 'unknown')
BRANCH_NAME="master"
```

### Example Branch-Specific URLs

```bash
# Master branch
BRANCH_NAME="master"
INTERNAL_API_ENDPOINT="http://discordbot-energex-jkhvk-web.discordbot-energex-jkhvk.svc.cluster.local:8080"

# Staging branch  
BRANCH_NAME="staging"
INTERNAL_API_ENDPOINT="http://discordbot-energex-staging-web.discordbot-energex-staging.svc.cluster.local:8080"

# Development branch
BRANCH_NAME="develop"
INTERNAL_API_ENDPOINT="http://discordbot-energex-develop-web.discordbot-energex-develop.svc.cluster.local:8080"
```

## Available Endpoints

### Health & Status
- `GET /internal/health` - Health check
- `GET /internal/branch-info` - Get branch information
- `GET /internal/network/status` - Network status across all branches
- `GET /internal/network/ping/:branch` - Ping specific branch

### Data Synchronization
- `POST /internal/network/sync/guild/:guildId/features` - Sync guild features to another branch
- `GET /internal/network/guild/:guildId/from/:branch` - Get guild data from another branch
- `POST /internal/sync/features` - Handle incoming feature sync (internal)
- `POST /internal/sync/config` - Handle incoming config sync (internal)

### Broadcasting
- `POST /internal/network/broadcast` - Broadcast message to all branches
- `POST /internal/broadcast` - Handle incoming broadcast (internal)

### Testing
- `POST /internal/network/test` - Test network connectivity

## Usage Examples

### Check Network Status
```bash
GET /internal/network/status
```

### Sync Guild Features
```bash
POST /internal/network/sync/guild/123456789/features
Content-Type: application/json

{
  "targetBranch": "staging"
}
```

### Ping Another Branch
```bash
GET /internal/network/ping/master
```

### Broadcast to All Branches
```bash
POST /internal/network/broadcast
Content-Type: application/json

{
  "message": {
    "type": "maintenance",
    "text": "Scheduled maintenance in 30 minutes"
  },
  "branches": ["master", "staging"]
}
```

## Features

- **Branch-to-Branch Communication**: Direct HTTP communication between deployments
- **Guild Data Syncing**: Synchronize guild configurations and features
- **Network Health Monitoring**: Monitor status of all branch deployments
- **Broadcasting**: Send messages to multiple branches simultaneously
- **Error Handling**: Graceful handling of network failures and timeouts
- **No Authentication Required**: Assumes secure internal network

## Implementation Details

### Service Architecture
- `InternalNetworkService`: Core service handling all network operations
- `InternalNetworkController`: REST API endpoints for internal communication
- URL transformation for branch-specific endpoints

### URL Transformation
The service automatically transforms URLs for different branches:
- Base: `http://service.domain.com`
- Target branch `staging`: `http://service-staging.domain.com`

### Timeout & Error Handling
- 5-second timeout for all internal requests
- Graceful degradation when branches are unreachable
- Detailed error logging for debugging

## Security

Since this uses internal URLs without authentication, ensure:
1. Internal endpoints are only accessible within your infrastructure
2. Network policies prevent external access to internal endpoints
3. Use proper firewall rules and network segmentation