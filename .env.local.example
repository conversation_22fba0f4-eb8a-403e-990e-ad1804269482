# Local Development Environment Variables
# Copy this to .env.local for local development

# Local URLs
APP_URL="http://localhost:3000"
NEXT_PUBLIC_API_ENDPOINT="http://localhost:8080"
INTERNAL_API_ENDPOINT="http://localhost:8080"
WEB_URL="http://localhost:3000"

# Local Development Settings
NODE_ENV=development
ENABLE_ENV_LOGIN=true

# Cookie Configuration for Local Development
# Leave COOKIE_DOMAIN empty for localhost compatibility
# The system will auto-detect the appropriate domain
# COOKIE_DOMAIN= (leave empty for localhost)

# Discord Configuration (use same values)
BOT_CLIENT_ID="1394521471862308884"
BOT_CLIENT_SECRET="[YOUR_SECRET]"
NEXT_PUBLIC_BOT_CLIENT_ID="1394521471862308884"
DISCORD_TOKEN="[YOUR_BOT_TOKEN]"

# Database and other services (same as production)
DATABASE_URL="[YOUR_DATABASE_URL]"