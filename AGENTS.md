# AGENTS.md - Development Guidelines

## Build/Test/Lint Commands
- `npm run build` - Build Next.js application
- `npm run dev` - Start development server on port 8080
- `npm run start` - Start production server on port 8080
- `npm run test:api` - Run Discord API tests (JS)
- `npm run test:api:ts` - Run Discord API tests (TypeScript)
- `npm run test:discord` - Run all Discord integration tests
- `npm run test:whop` - Run Whop integration tests
- `npx ts-node tests/[filename].ts` - Run single test file

## Code Style Guidelines
- **TypeScript**: Strict mode enabled with enhanced error checking
- **Imports**: Use absolute paths with `@/` prefix for lib imports
- **Formatting**: Prettier with single quotes, 100 char line width
- **Naming**: camelCase for variables/functions, PascalCase for types/interfaces
- **Error Handling**: Always use try-catch blocks, log errors with context
- **Types**: Explicit typing required, avoid `any`, use interfaces for objects
- **Functions**: JSDoc comments for public APIs, descriptive parameter names
- **Database**: Use service layer pattern (e.g., UserService, SessionService)
- **API Routes**: Follow Next.js App Router patterns, validate inputs with Zod
- **Security**: Never log sensitive data, use environment variables for secrets