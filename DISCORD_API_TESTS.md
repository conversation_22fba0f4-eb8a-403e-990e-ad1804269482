# Discord API Integration Test Suite

This directory contains comprehensive test scripts to verify the Discord API endpoints and integrations without requiring actual Discord tokens or live connections.

## Overview

The test suite validates:
- **Backend API Structure**: Guild controller endpoints and data handling
- **Frontend API Integration**: Discord.ts and bot.ts API calls
- **Permission System**: Discord permission flags and validation
- **Channel Types**: Discord channel type definitions and usage
- **Database Services**: All feature configuration interfaces
- **Data Structures**: Type validation and consistency
- **Error Handling**: Proper error responses and edge cases
- **Security**: Input validation and authentication requirements

## Test Files

### 1. `test-discord-api.js` (JavaScript Version)
- Standalone Node.js script
- No TypeScript dependencies required
- Compatible with any Node.js environment
- Run with: `npm run test:api`

### 2. `test-discord-api.ts` (TypeScript Version)
- Full TypeScript integration testing
- Compile-time type checking
- Interface validation
- Enhanced IDE support
- Run with: `npm run test:api:ts` or `npm run test:discord`

## Quick Start

### Prerequisites
```bash
# Ensure Node.js 18.x is installed
node --version

# Install dependencies (if not already done)
npm install
```

### Running Tests

```bash
# Run TypeScript version (recommended)
npm run test:discord

# Run JavaScript version
npm run test:api

# Run TypeScript version directly
npm run test:api:ts
```

## Test Coverage

### 🔐 Discord Permission System
- ✅ Permission flag bit values validation
- ✅ Permission combination (bitwise operations)
- ✅ Required permissions availability
- ✅ Administrator and management permissions

### 📢 Discord Channel Types
- ✅ Channel type numeric values
- ✅ All Discord channel types defined
- ✅ Text, Voice, Category channel validation
- ✅ Thread and Forum channel support

### 📊 Data Structure Validation
- ✅ UserInfo interface structure
- ✅ Guild interface structure
- ✅ Channel interface structure
- ✅ Role interface structure
- ✅ Type consistency across frontend/backend

### 🎮 Guild Controller Endpoints
Tests all REST API endpoints:
```
GET    /guilds/:guild                    - Guild information
GET    /guilds/:guild/channels           - Guild channels
GET    /guilds/:guild/roles              - Guild roles
GET    /guilds/:guild/stats              - Guild statistics

// Feature endpoints (CRUD operations)
GET    /guilds/:guild/features/:feature  - Get feature config
POST   /guilds/:guild/features/:feature  - Enable feature
PATCH  /guilds/:guild/features/:feature  - Update feature
DELETE /guilds/:guild/features/:feature  - Disable feature
```

Tested features:
- `welcome-message` - Welcome message configuration
- `music` - Music bot settings
- `gaming` - Gaming features
- `reaction-role` - Reaction role system
- `meme` - Meme posting
- `user-command` - Custom user commands
- `leveling` - XP and leveling system
- `moderation` - Auto-moderation
- `economy` - Economy system
- `utility` - Utility commands
- `starboard` - Message starboard

### 🗄️ Database Service Interfaces
- ✅ WelcomeMessageConfig interface
- ✅ MusicConfig interface
- ✅ GamingConfig interface
- ✅ ReactionRoleConfig interface
- ✅ MemeConfig interface
- ✅ UserCommandConfig interface
- ✅ LevelingConfig interface
- ✅ ModerationConfig interface
- ✅ EconomyConfig interface
- ✅ UtilityConfig interface
- ✅ StarboardConfig interface

### 🔗 Discord API Integration
- ✅ `/users/@me` endpoint structure
- ✅ `/users/@me/guilds` endpoint structure
- ✅ `/guilds/:id` endpoint structure
- ✅ Response format validation
- ✅ Error handling (401, 403, 404)

### 🔒 Security Validation
- ✅ Authentication requirements
- ✅ Permission validation simulation
- ✅ Input sanitization checks
- ✅ SQL injection prevention (simulated)
- ✅ Access control verification

### ⚡ TypeScript Integration (TS version only)
- ✅ Interface compilation
- ✅ Enum type safety
- ✅ Function parameter typing
- ✅ Return type validation
- ✅ Compile-time error detection

## Mock Data

The test suite uses comprehensive mock data that mirrors real Discord API responses:

```typescript
// Example mock guild
{
  id: "123456789012345678",
  name: "Test Guild",
  icon: "test_icon_hash",
  enabledFeatures: ["welcome-message", "music", "gaming"]
}

// Example mock user
{
  id: "987654321098765432",
  username: "TestUser",
  discriminator: "1234",
  avatar: "test_avatar_hash",
  mfa_enabled: false,
  locale: "en-US"
}
```

## Test Results

The test suite provides detailed reporting:

```
📊 Test Report Summary
==================================================
Total Tests: 45
Passed: 45 ✅
Failed: 0 ❌
Success Rate: 100.0%

📋 Test Coverage Areas:
• Discord Permission System ✅
• Channel Types and Validation ✅
• Data Structure Validation ✅
• Guild Controller Endpoints ✅
• Discord API Integration ✅
• Feature Configurations ✅
• Error Handling ✅
• API Consistency ✅
• Security Validation ✅
```

## Configuration

### Test Configuration
```javascript
const TEST_CONFIG = {
    MOCK_GUILD_ID: '123456789012345678',
    MOCK_USER_ID: '987654321098765432',
    MOCK_CHANNEL_ID: '111222333444555666',
    MOCK_ROLE_ID: '777888999000111222',
    API_BASE_URL: 'http://localhost:3001',
    DISCORD_API_URL: 'https://discord.com/api/v10'
};
```

### Customization
You can modify the test configuration by editing the constants at the top of either test file:

- Change mock IDs for different test scenarios
- Adjust API endpoints for different environments
- Add additional mock data for extended testing

## Integration with CI/CD

### GitHub Actions Example
```yaml
name: Discord API Tests

on: [push, pull_request]

jobs:
  test-discord-api:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        
    - name: Install dependencies
      run: npm install
      
    - name: Run Discord API tests
      run: npm run test:discord
```

### Pre-commit Hook
```bash
#!/bin/sh
# .git/hooks/pre-commit
npm run test:discord
if [ $? -ne 0 ]; then
  echo "Discord API tests failed. Commit aborted."
  exit 1
fi
```

## Troubleshooting

### Common Issues

1. **TypeScript compilation errors**
   ```bash
   # Install ts-node if missing
   npm install -g ts-node
   ```

2. **Missing dependencies**
   ```bash
   # Reinstall dependencies
   rm -rf node_modules package-lock.json
   npm install
   ```

3. **Permission errors**
   ```bash
   # Make scripts executable
   chmod +x test-discord-api.js
   chmod +x test-discord-api.ts
   ```

### Debug Mode
Add `DEBUG=true` environment variable for verbose output:
```bash
DEBUG=true npm run test:discord
```

## Best Practices

### When to Run Tests
- Before committing code changes
- After modifying API endpoints
- Before deploying to production
- When adding new features
- During code reviews

### Adding New Tests
1. Add test functions to both JS and TS versions
2. Include proper type definitions (TS version)
3. Update mock data if needed
4. Add test coverage to this README
5. Ensure tests are deterministic

### Maintenance
- Update Discord API constants when Discord updates their API
- Keep mock data synchronized with real API responses
- Review test coverage when adding new features
- Update dependencies regularly

## Related Files

- `api/controllers/guild.controller.ts` - Main backend controller
- `utils/database.ts` - Database service interfaces
- `../src/api/discord.ts` - Frontend Discord API integration
- `../src/api/bot.ts` - Frontend bot API integration
- `config.ts` - API configuration

## Contributing

When adding new features or endpoints:

1. Add corresponding test cases to both test files
2. Update mock data to include new structures
3. Verify type definitions match implementation
4. Run tests locally before submitting PR
5. Update this documentation

## License

This test suite is part of the Discord Bot Dashboard project and follows the same license terms.