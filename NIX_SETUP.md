# Discord Bot - Nix Environment Setup

This guide provides solutions for running the Discord bot consistently in a Nix environment.

## 🚀 Quick Start

### Option 1: Using Nix Shell (Recommended for Development)

```bash
# Enter the Nix development environment
nix-shell

# Start the bot in development mode
pnpm run dev:runner

# Or use the managed process runner
pnpm run start:managed
```

### Option 2: Using Process Management Scripts

```bash
# Start the bot with automatic restart capabilities
./scripts/start-bot.sh start

# Monitor the bot (auto-restart on failure)
./scripts/start-bot.sh monitor

# Check status
./scripts/start-bot.sh status

# View logs
./scripts/start-bot.sh logs
```

### Option 3: Using Development Runner

```bash
# Start development server with TypeScript error handling
./scripts/dev-runner.sh dev

# Start production server
./scripts/dev-runner.sh prod

# Clean install dependencies
./scripts/dev-runner.sh clean
```

## 📋 Available Commands

### Package.json Scripts

| Command | Description |
|---------|-------------|
| `pnpm run start:managed` | Start with process management |
| `pnpm run stop:managed` | Stop managed process |
| `pnpm run restart:managed` | Restart managed process |
| `pnpm run status` | Check bot status |
| `pnpm run monitor` | Start monitoring mode |
| `pnpm run dev:runner` | Development mode with error handling |
| `pnpm run prod:runner` | Production mode |
| `pnpm run logs` | View recent logs |
| `pnpm run health` | Check bot health |
| `pnpm run clean` | Clean install |

### Process Management Script

```bash
./scripts/start-bot.sh {start|stop|restart|status|monitor|logs}
```

- **start**: Start the Discord bot
- **stop**: Stop the Discord bot  
- **restart**: Restart the Discord bot
- **status**: Show bot status and recent logs
- **monitor**: Start monitoring with auto-restart
- **logs**: Show recent logs (default: 50 lines)

### Development Runner Script

```bash
./scripts/dev-runner.sh {dev|prod|build|test|clean|fix-ts|check-env}
```

- **dev**: Start development server with auto-restart
- **prod**: Start production server
- **build**: Build the application
- **test**: Run tests
- **clean**: Clean and reinstall dependencies
- **fix-ts**: Fix TypeScript issues
- **check-env**: Check environment configuration

## 🔧 Troubleshooting

### Common Issues

#### 1. TypeScript Compilation Errors

```bash
# Fix TypeScript issues
./scripts/dev-runner.sh fix-ts

# Or force build with errors
pnpm run build:force
```

#### 2. Bot Keeps Crashing

```bash
# Use monitoring mode for auto-restart
./scripts/start-bot.sh monitor

# Check logs for errors
./scripts/start-bot.sh logs 100
```

#### 3. Environment Issues

```bash
# Check environment variables
./scripts/dev-runner.sh check-env

# Verify Node.js and dependencies
nix-shell --run "node --version && pnpm --version"
```

#### 4. Dependency Issues

```bash
# Clean reinstall
./scripts/dev-runner.sh clean

# Or manually
pnpm run clean
```

### Log Files

All logs are stored in the `./logs/` directory:

- `discord-bot-energex.log` - Main application logs
- `discord-bot-energex.error.log` - Error logs
- `systemd.log` - Systemd service logs (if using systemd)
- `systemd.error.log` - Systemd error logs

## 🔄 Systemd Service (Optional)

For persistent running across reboots:

```bash
# Copy service file
sudo cp scripts/discord-bot.service /etc/systemd/system/

# Enable and start service
sudo systemctl enable discord-bot.service
sudo systemctl start discord-bot.service

# Check status
sudo systemctl status discord-bot.service
```

## 🛠️ Development Workflow

### 1. Initial Setup

```bash
# Enter Nix environment
nix-shell

# Install dependencies
pnpm install

# Check environment
./scripts/dev-runner.sh check-env
```

### 2. Development

```bash
# Start development server
pnpm run dev:runner

# Or with monitoring
pnpm run monitor
```

### 3. Production Deployment

```bash
# Build and start production
./scripts/dev-runner.sh prod

# Or use managed process
pnpm run start:managed
```

## 📊 Monitoring

### Real-time Monitoring

```bash
# Monitor with auto-restart
./scripts/start-bot.sh monitor

# Watch logs in real-time
tail -f logs/discord-bot-energex.log
```

### Health Checks

```bash
# Check bot health
pnpm run health

# Check status
pnpm run status
```

## 🔐 Environment Variables

Ensure these are set in your `.env` file:

```env
DISCORD_TOKEN=your_discord_token
DATABASE_URL=your_database_url
NODE_ENV=production
PORT=8080
```

## 🎯 Features

- ✅ Automatic restart on failure
- ✅ TypeScript error handling
- ✅ Process management
- ✅ Comprehensive logging
- ✅ Health monitoring
- ✅ Nix environment support
- ✅ Development and production modes
- ✅ Systemd integration
- ✅ Clean dependency management

## 📝 Notes

- The bot will automatically restart up to 5 times on failure
- Logs are rotated and stored in the `logs/` directory
- Use `nix-shell` for the most consistent environment
- Monitor mode is recommended for production use
- All scripts are designed to work in both Nix and standard environments
