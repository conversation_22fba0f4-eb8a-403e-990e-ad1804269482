# Sevalla Deployment Guide

This guide explains how to deploy the dashboard application to Sevalla.

## Prerequisites

- Sevalla account
- GitHub repository connected to Sevalla
- Production environment variables configured

## Configuration Files

### 1. `sevalla.json`
Main configuration file for Sevalla deployment with:
- Build commands and settings
- Health check configuration
- Pipeline configuration
- Deployment features

### 2. `zeabur.json`
Legacy configuration file updated with health check path.

### 3. Health Check Endpoint
- **Path**: `/api/health`
- **Method**: GET
- **Response**: JSON with service status

## Deployment Steps

### 1. Environment Variables
Configure these environment variables in your Sevalla dashboard:

```bash
BOT_CLIENT_ID="your_bot_client_id"
BOT_CLIENT_SECRET="your_bot_client_secret"
NEXT_PUBLIC_BOT_CLIENT_ID="your_bot_client_id"
APP_URL="https://your-app-name.sevalla.app"
NEXT_PUBLIC_API_ENDPOINT="https://your-backend-name.sevalla.app"
INTERNAL_API_ENDPOINT="https://your-backend-name.sevalla.app"
NODE_ENV="production"
PORT="3000"
ENABLE_ENV_LOGIN="false"
NEXT_TELEMETRY_DISABLED="1"
```

### 2. Build Configuration
- **Build Command**: `pnpm run build`
- **Start Command**: `pnpm start`
- **Port**: 3000
- **Health Check**: `/api/health`

### 3. Pipeline Configuration
The application is configured for trunk-based development with:
- **Development**: Auto-deploy from `develop` branch
- **Staging**: Auto-deploy from `staging` branch
- **Production**: Manual deploy from `master` branch

### 4. Features Enabled
- ✅ Preview Apps
- ✅ Edge Caching
- ✅ CDN
- ✅ Zero Downtime Deployments
- ✅ Auto Deployments

## Deployment Commands

```bash
# Build for production
pnpm run sevalla:build

# Start production server
pnpm run sevalla:start

# Health check
pnpm run sevalla:health
```

## Monitoring

The health check endpoint provides:
- Service status
- Timestamp
- Service name
- Version information

## Troubleshooting

### Common Issues

1. **Build Failures**
   - Check build logs in Sevalla dashboard
   - Verify all dependencies are in `package.json`
   - Ensure build command is correct

2. **Health Check Failures**
   - Verify `/api/health` endpoint is accessible
   - Check application logs
   - Ensure port configuration is correct

3. **Environment Variables**
   - Verify all required environment variables are set
   - Check variable names and values
   - Ensure production URLs are correct

## GitHub Actions (Optional)

A GitHub Actions workflow template is available in `sevalla-deploy-workflow.yml`. To use it:

1. Move the file to `.github/workflows/sevalla-deploy.yml`
2. Set up the required GitHub secrets:
   - `SEVALLA_API_KEY`
   - `SEVALLA_PROJECT_ID`
   - `SEVALLA_DEPLOYMENT_URL`
3. Ensure your GitHub App has workflows permission

## Support

For Sevalla-specific issues, refer to:
- [Sevalla Documentation](https://docs.sevalla.com/)
- [Sevalla Support](https://sevalla.com/support/)