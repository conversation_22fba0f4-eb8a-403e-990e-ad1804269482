# Backend Sevalla Deployment Checklist

## Pre-Deployment Setup

### 1. Sevalla Account Setup
- [ ] Create separate Sevalla application for backend
- [ ] Connect GitHub repository (backend branch)
- [ ] Generate API key for backend deployments

### 2. Database Setup
- [ ] Set up PostgreSQL database in Sevalla
- [ ] Configure database connection pooling
- [ ] Run initial database migrations
- [ ] Set up database backup strategy

### 3. Environment Configuration
- [ ] Set up environment variables in Sevalla dashboard:
  - [ ] `NODE_ENV=production`
  - [ ] `PORT=8080`
  - [ ] `DISCORD_BOT_TOKEN`
  - [ ] `DISCORD_CLIENT_ID`
  - [ ] `DISCORD_CLIENT_SECRET`
  - [ ] `DATABASE_URL`
  - [ ] `DB_HOST`
  - [ ] `DB_PORT`
  - [ ] `DB_NAME`
  - [ ] `DB_USER`
  - [ ] `DB_PASSWORD`
  - [ ] `WEB_URL`
  - [ ] `FRONTEND_URL`
  - [ ] `INTERNAL_API_ENDPOINT`
  - [ ] `WHOP_API_KEY`
  - [ ] `WHOP_APP_ID`
  - [ ] `CORS_ORIGIN`

### 4. GitHub Secrets (if using GitHub Actions)
- [ ] `SEVALLA_API_KEY`
- [ ] `SEVALLA_BACKEND_PROJECT_ID`
- [ ] `SEVALLA_BACKEND_URL`

## Deployment Configuration

### 5. Application Settings
- [ ] Build Command: `pnpm run build`
- [ ] Start Command: `pnpm run start:prod`
- [ ] Port: `8080`
- [ ] Health Check Path: `/health`
- [ ] Framework: NestJS
- [ ] Node Version: 18.x
- [ ] Enable zero-downtime deployments

### 6. Pipeline Configuration
- [ ] Set up trunk-based development pipeline
- [ ] Configure automatic deployment for backend branch
- [ ] Set up staging environment
- [ ] Configure manual production deployment

### 7. Performance Optimizations
- [ ] Enable CDN for static assets
- [ ] Configure edge caching
- [ ] Set up build cache
- [ ] Configure health check monitoring

## Testing & Validation

### 8. Local Testing
- [ ] Install dependencies: `pnpm install`
- [ ] Build application: `pnpm run build`
- [ ] Start application: `pnpm run start:prod`
- [ ] Test health endpoint: `curl http://localhost:8080/health`
- [ ] Test Discord API integration: `pnpm run test:discord:all`
- [ ] Test Whop integration: `pnpm run test:whop`

### 9. Deployment Testing
- [ ] Deploy to development environment
- [ ] Verify health check passes
- [ ] Test all API endpoints
- [ ] Verify database connectivity
- [ ] Test Discord bot integration
- [ ] Test Whop integration
- [ ] Verify CORS configuration

### 10. Production Deployment
- [ ] Deploy to staging first
- [ ] Run integration tests
- [ ] Monitor performance metrics
- [ ] Test with frontend application
- [ ] Deploy to production
- [ ] Monitor logs for errors

## Post-Deployment

### 11. Monitoring
- [ ] Set up application monitoring
- [ ] Configure health check alerts
- [ ] Monitor API response times
- [ ] Set up database monitoring
- [ ] Configure error tracking

### 12. Documentation
- [ ] Update API documentation
- [ ] Document deployment process
- [ ] Update environment configuration docs
- [ ] Create troubleshooting guide

## Security Checklist

### 13. Security Configuration
- [ ] Verify all secrets are environment variables
- [ ] Check CORS configuration
- [ ] Validate authentication middleware
- [ ] Ensure HTTPS in production
- [ ] Check rate limiting configuration

## Files Created/Modified

### Configuration Files
- [x] `sevalla.json` - Main Sevalla configuration for backend
- [x] `api/controllers/app.controller.ts` - Added health check endpoint
- [x] `.env.sevalla` - Production environment template

### Documentation
- [x] `SEVALLA_BACKEND_DEPLOYMENT.md` - Backend deployment guide
- [x] `BACKEND_DEPLOYMENT_CHECKLIST.md` - This checklist

### CI/CD
- [x] `sevalla-backend-workflow.yml` - GitHub Actions template (for manual installation)

### Package Configuration
- [x] `package.json` - Added Sevalla-specific scripts

## API Endpoints Available

### Health Check
- **GET** `/health` - Application health status

### Discord Integration
- **GET** `/guilds/:guild` - Get guild information
- **GET** `/guilds/:guild/channels` - Get guild channels
- **GET** `/guilds/:guild/roles` - Get guild roles
- **GET** `/guilds/:guild/members` - Get guild members
- **GET** `/guilds/:guild/stats` - Get guild statistics

### Feature Management
- **GET/POST/PATCH/DELETE** `/guilds/:guild/features/*` - Manage guild features

### Whop Integration
- **GET** `/whop/status` - Whop integration status
- **GET** `/whop/user/me` - Current user info
- **GET** `/whop/user/:userId/memberships` - User memberships

### Internal Network
- **GET** `/internal/health` - Internal health check
- **GET** `/internal/branch-info` - Branch information
- **POST** `/internal/sync/*` - Data synchronization endpoints

## Troubleshooting

### Common Issues

1. **Health Check Failures**
   - Check application logs
   - Verify port configuration
   - Ensure all dependencies are installed

2. **Database Connection Issues**
   - Verify DATABASE_URL
   - Check database credentials
   - Ensure database is accessible

3. **Discord API Issues**
   - Verify bot token
   - Check bot permissions
   - Monitor rate limits

4. **Whop Integration Issues**
   - Verify Whop API key
   - Check Whop app configuration
   - Monitor webhook endpoints

## Emergency Procedures

### Rollback Plan
- [ ] Document rollback procedure
- [ ] Test rollback in staging
- [ ] Prepare incident response
- [ ] Set up monitoring alerts