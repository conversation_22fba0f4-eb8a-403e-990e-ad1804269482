#!/usr/bin/env node

/**
 * Simple test runner for Discord API tests
 * Works without external dependencies
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Discord API Test Runner');
console.log('==========================');

// Check which test files exist
const jsTestFile = path.join(__dirname, 'test-discord-api.js');
const tsTestFile = path.join(__dirname, 'test-discord-api.ts');

const jsExists = fs.existsSync(jsTestFile);
const tsExists = fs.existsSync(tsTestFile);

console.log(`JavaScript test file: ${jsExists ? '✅ Found' : '❌ Missing'}`);
console.log(`TypeScript test file: ${tsExists ? '✅ Found' : '❌ Missing'}`);

function runCommand(command, args = []) {
    return new Promise((resolve, reject) => {
        console.log(`\n🔄 Running: ${command} ${args.join(' ')}`);
        
        const child = spawn(command, args, {
            stdio: 'inherit',
            shell: true
        });
        
        child.on('close', (code) => {
            if (code === 0) {
                console.log(`✅ ${command} completed successfully`);
                resolve(code);
            } else {
                console.log(`❌ ${command} failed with code ${code}`);
                reject(new Error(`Command failed with code ${code}`));
            }
        });
        
        child.on('error', (error) => {
            console.error(`💥 Error running ${command}:`, error.message);
            reject(error);
        });
    });
}

async function runTests() {
    try {
        if (jsExists) {
            console.log('\n📝 Running JavaScript tests...');
            await runCommand('node', ['test-discord-api.js']);
        }
        
        // Check if ts-node is available
        try {
            if (tsExists) {
                console.log('\n⚡ Checking TypeScript environment...');
                await runCommand('npx', ['ts-node', '--version']);
                
                console.log('\n📝 Running TypeScript tests...');
                await runCommand('npx', ['ts-node', 'test-discord-api.ts']);
            }
        } catch (error) {
            console.log('\n⚠️  TypeScript test skipped (ts-node not available)');
            console.log('   Install with: npm install ts-node');
        }
        
        console.log('\n🎉 All available tests completed successfully!');
        console.log('\n📋 Quick Setup Guide:');
        console.log('• To install ts-node: npm install ts-node');
        console.log('• To run JS tests: npm run test:api');
        console.log('• To run TS tests: npm run test:api:ts');
        console.log('• To run all tests: npm run test:discord');
        
    } catch (error) {
        console.error('\n💥 Test execution failed:', error.message);
        process.exit(1);
    }
}

if (require.main === module) {
    runTests();
}

module.exports = { runTests };