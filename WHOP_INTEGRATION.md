# Whop Integration for EnergeX Discord Bot

This document describes the Whop integration feature that has been added to the EnergeX Discord bot.

## Overview

The Whop integration allows Discord server owners to:
- Verify user memberships through Whop
- Automatically assign roles based on membership status
- Sync membership data with Discord roles
- Handle webhook notifications from Whop
- Manage premium and member access

## Features

### ✅ Implemented Features

1. **Membership Verification**
   - Verify user access to specific Whop access passes
   - Automatic member verification on join
   - Manual verification commands

2. **Role Management**
   - Automatic role assignment for verified members
   - Premium role assignment for premium members
   - Role sync based on membership status

3. **Configuration Management**
   - Web dashboard for configuration
   - Company and access pass settings
   - Webhook URL configuration
   - Channel and role selection

4. **API Integration**
   - Full Whop GraphQL API integration
   - User membership validation
   - Access pass management
   - Company data retrieval

5. **Database Support**
   - PostgreSQL table for configuration storage
   - Migration scripts included
   - Proper indexing for performance

## Setup Instructions

### 1. Environment Configuration

Add the following environment variables to your `.env` file:

```env
# Whop API Configuration
WHOP_API_KEY="your_whop_api_key_here"
NEXT_PUBLIC_WHOP_APP_ID="your_whop_app_id_here"
NEXT_PUBLIC_WHOP_AGENT_USER_ID="your_whop_agent_user_id_here"
NEXT_PUBLIC_WHOP_COMPANY_ID="your_whop_company_id_here"
```

### 2. Database Setup

Run the database migration to create the Whop table:

```sql
-- Run the migration script
\i backend/migrations/create_whop_table.sql
```

### 3. Whop App Configuration

1. Go to [Whop Developer Dashboard](https://whop.com/apps)
2. Create a new app or use an existing one
3. Get your API keys from the "API Keys" section
4. Configure your app settings

### 4. Testing the Integration

Run the integration test to verify everything is working:

```bash
cd backend
npm run test:whop
```

## Usage

### Dashboard Configuration

1. Navigate to your guild settings in the EnergeX dashboard
2. Go to Features → Whop Integration
3. Enable the Whop integration
4. Configure your settings:
   - Company ID
   - Access Pass ID
   - Verification settings
   - Role assignments
   - Webhook configuration

### Available Settings

- **Basic Configuration**
  - Enable/disable integration
  - Company ID
  - Access Pass ID

- **Member Verification**
  - Require verification
  - Auto-verify members
  - Verification channel
  - Welcome message

- **Role Management**
  - Sync roles
  - Member role
  - Premium role
  - Log channel

- **Advanced Settings**
  - Enable webhooks
  - Webhook URL
  - Sync interval

## API Endpoints

The following API endpoints are available:

### Status
- `GET /api/whop/status` - Check integration status

### User Management
- `GET /api/whop/user/me` - Get current user
- `GET /api/whop/user/:userId` - Get user by ID
- `GET /api/whop/user/:userId/memberships` - Get user memberships
- `GET /api/whop/user/:userId/access/:accessPassId` - Validate user access

### Company Management
- `GET /api/whop/company/:companyId/access-passes` - Get access passes

### Guild Configuration
- `GET /api/whop/guild/:guildId/config` - Get guild configuration
- `POST /api/whop/guild/:guildId/config` - Update guild configuration

### Webhooks
- `POST /api/whop/webhook/test` - Test webhook endpoint

## File Structure

```
├── backend/
│   ├── api/
│   │   ├── controllers/
│   │   │   └── whop.controller.ts
│   │   └── services/
│   │       └── whop.service.ts
│   ├── lib/
│   │   └── whop-sdk.ts
│   ├── migrations/
│   │   └── create_whop_table.sql
│   └── test-whop-integration.ts
├── src/
│   ├── api/
│   │   ├── hooks.ts (updated)
│   │   └── whop.ts
│   ├── config/
│   │   ├── features/
│   │   │   └── WhopFeature.tsx
│   │   ├── features.tsx (updated)
│   │   └── types/
│   │       └── custom-types.ts (updated)
│   └── lib/
│       └── whop-sdk.ts
└── WHOP_INTEGRATION.md
```

## Troubleshooting

### Common Issues

**TypeScript Module Resolution Error**
If you encounter an error like "Cannot find module '@whop/api'", this is due to TypeScript module resolution issues. The current implementation uses a mock SDK to avoid build errors. To enable the real Whop SDK:

1. Update your `tsconfig.json` to use `"moduleResolution": "bundler"`
2. Or try using `"moduleResolution": "node16"` or `"nodenext"`
3. Ensure the `@whop/api` package is properly installed
4. Replace the mock implementation in `lib/whop-sdk.ts` with the real import

### Other Issues

1. **API Connection Failed**
   - Check your API keys in the `.env` file
   - Verify your Whop app is properly configured
   - Ensure your app has the necessary permissions

2. **Database Errors**
   - Run the migration script to create the Whop table
   - Check your database connection
   - Verify PostgreSQL is running

3. **Role Assignment Not Working**
   - Check that the bot has permission to manage roles
   - Verify the role IDs are correct
   - Ensure the bot's role is higher than the roles it's trying to assign

### Getting Help

If you encounter issues:
1. Check the console logs for error messages
2. Run the test script to verify the integration
3. Check the Whop API documentation
4. Verify your environment configuration

## Contributing

When contributing to the Whop integration:
1. Follow the existing code patterns
2. Add tests for new functionality
3. Update this documentation
4. Test thoroughly before submitting

## License

This integration is part of the EnergeX Discord bot and follows the same license terms.
