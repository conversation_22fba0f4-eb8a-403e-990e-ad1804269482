# 🔄 Refresh Command Documentation

## Overview

The `/refresh` command allows administrators to refresh bot settings and commands without restarting the entire Discord bot. This is useful for applying configuration changes, reloading command cache, and refreshing AI agent settings.

## Command Syntax

```
/refresh [component]
```

### Parameters

- **component** (optional): Specific component to refresh
  - `commands` - Refresh Discord slash commands
  - `agents` - Refresh AI agent configurations
  - `config` - Refresh bot configuration settings
  - `all` - Refresh all components (default)

## Permissions

- **Required**: Administrator or Manage Server permissions
- **Scope**: Guild-specific (works per Discord server)
- **Access**: Admin users only

## Usage Examples

### Refresh All Components
```
/refresh
```
or
```
/refresh component:All Components
```

### Refresh Specific Components
```
/refresh component:Commands
/refresh component:AI Agents
/refresh component:Configuration
```

## What Gets Refreshed

### 🎮 Commands (`commands`)
- Clears Discord command cache
- Refreshes global application commands
- Refreshes guild-specific commands
- Updates command metadata and options

### 🤖 AI Agents (`agents`)
- Reloads AI agent configurations
- Clears agent memory caches
- Refreshes agent personality settings
- Updates agent routing rules

### ⚙️ Configuration (`config`)
- Reloads environment variables
- Refreshes configuration caches
- Updates feature flags
- Reloads database connection settings

### 🔄 All Components (`all`)
- Performs all of the above operations
- Comprehensive system refresh
- Recommended for major configuration changes

## Response Format

The command returns a detailed embed with:

```
🔄 Bot Refresh Results

✅ Commands refreshed successfully
✅ AI Agents refreshed successfully  
✅ Configuration refreshed successfully

📊 Component: All Components
⏱️ Timestamp: 1/24/2025, 8:51:02 PM
👤 Executed By: username
```

## Error Handling

### Permission Errors
```
❌ You need Administrator or Manage Server permissions to use this command.
```

### Component Errors
```
🔄 Bot Refresh Results

✅ Commands refreshed successfully
❌ Failed to refresh AI agents
✅ Configuration refreshed successfully

⚠️ Some components failed to refresh - check logs for details
```

### System Errors
```
❌ Failed to refresh bot components. Please check logs for details.
```

## Implementation Details

### Security Features
- Permission validation before execution
- Guild-specific operation scope
- Comprehensive error logging
- Safe failure handling

### Performance Considerations
- Non-blocking operations
- Graceful degradation on errors
- Minimal service interruption
- Efficient cache management

### Logging
All refresh operations are logged with:
- User who executed the command
- Components that were refreshed
- Success/failure status
- Detailed error messages for debugging

## Use Cases

### 1. Configuration Updates
When you update environment variables or configuration files:
```
/refresh component:Configuration
```

### 2. Command Changes
After deploying new slash commands or updating existing ones:
```
/refresh component:Commands
```

### 3. AI Agent Tuning
When modifying AI agent personalities or settings:
```
/refresh component:AI Agents
```

### 4. Full System Refresh
After major updates or when experiencing issues:
```
/refresh component:All Components
```

## Best Practices

### When to Use
- ✅ After configuration changes
- ✅ When commands aren't updating
- ✅ After AI agent modifications
- ✅ During troubleshooting
- ✅ After environment variable updates

### When NOT to Use
- ❌ During active user interactions
- ❌ As a regular maintenance task
- ❌ When the bot is functioning normally
- ❌ Multiple times in quick succession

### Recommendations
1. **Test in Development**: Always test refresh operations in a development environment first
2. **Monitor Logs**: Check application logs after refresh operations
3. **Inform Users**: Let users know when performing maintenance refreshes
4. **Use Specific Components**: Refresh only what's needed to minimize impact
5. **Verify Results**: Check that the refresh achieved the desired outcome

## Troubleshooting

### Command Not Appearing
1. Check if the bot has been restarted after adding the command
2. Verify the bot has permission to register slash commands
3. Try `/refresh component:Commands` to force command registration

### Permission Denied
1. Ensure you have Administrator or Manage Server permissions
2. Check if the bot has proper role hierarchy
3. Verify guild-specific permission settings

### Partial Failures
1. Check application logs for detailed error messages
2. Try refreshing individual components separately
3. Verify database connectivity and API keys
4. Ensure all required services are running

## Related Commands

- `/status` - Check bot health and status
- `/help` - Display available commands
- `/ping` - Test bot responsiveness

## Technical Notes

### Command Registration
The refresh command is automatically registered when the bot starts and uses the Necord framework for Discord.js integration.

### Database Impact
The refresh operation may temporarily increase database queries as configurations are reloaded and caches are rebuilt.

### Memory Usage
Refreshing components may cause temporary memory spikes as old caches are cleared and new ones are built.

## Version History

- **v1.0.0** - Initial implementation with basic refresh functionality
- **v1.0.1** - Added component-specific refresh options
- **v1.0.2** - Enhanced error handling and logging
- **v1.0.3** - Added permission validation and security improvements
